{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AIntegrity/aintegrity/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AIntegrity/aintegrity/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AIntegrity/aintegrity/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction DropdownMenu({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\n}\n\nfunction DropdownMenuPortal({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\n  return (\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\n  )\n}\n\nfunction DropdownMenuTrigger({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\n  return (\n    <DropdownMenuPrimitive.Trigger\n      data-slot=\"dropdown-menu-trigger\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuContent({\n  className,\n  sideOffset = 4,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\n  return (\n    <DropdownMenuPrimitive.Portal>\n      <DropdownMenuPrimitive.Content\n        data-slot=\"dropdown-menu-content\"\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\n          className\n        )}\n        {...props}\n      />\n    </DropdownMenuPrimitive.Portal>\n  )\n}\n\nfunction DropdownMenuGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\n  return (\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\n  )\n}\n\nfunction DropdownMenuItem({\n  className,\n  inset,\n  variant = \"default\",\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\n  inset?: boolean\n  variant?: \"default\" | \"destructive\"\n}) {\n  return (\n    <DropdownMenuPrimitive.Item\n      data-slot=\"dropdown-menu-item\"\n      data-inset={inset}\n      data-variant={variant}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuCheckboxItem({\n  className,\n  children,\n  checked,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\n  return (\n    <DropdownMenuPrimitive.CheckboxItem\n      data-slot=\"dropdown-menu-checkbox-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      checked={checked}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.CheckboxItem>\n  )\n}\n\nfunction DropdownMenuRadioGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\n  return (\n    <DropdownMenuPrimitive.RadioGroup\n      data-slot=\"dropdown-menu-radio-group\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuRadioItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\n  return (\n    <DropdownMenuPrimitive.RadioItem\n      data-slot=\"dropdown-menu-radio-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CircleIcon className=\"size-2 fill-current\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.RadioItem>\n  )\n}\n\nfunction DropdownMenuLabel({\n  className,\n  inset,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.Label\n      data-slot=\"dropdown-menu-label\"\n      data-inset={inset}\n      className={cn(\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\n  return (\n    <DropdownMenuPrimitive.Separator\n      data-slot=\"dropdown-menu-separator\"\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuShortcut({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) {\n  return (\n    <span\n      data-slot=\"dropdown-menu-shortcut\"\n      className={cn(\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSub({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\n}\n\nfunction DropdownMenuSubTrigger({\n  className,\n  inset,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.SubTrigger\n      data-slot=\"dropdown-menu-sub-trigger\"\n      data-inset={inset}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <ChevronRightIcon className=\"ml-auto size-4\" />\n    </DropdownMenuPrimitive.SubTrigger>\n  )\n}\n\nfunction DropdownMenuSubContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\n  return (\n    <DropdownMenuPrimitive.SubContent\n      data-slot=\"dropdown-menu-sub-content\"\n      className={cn(\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport {\n  DropdownMenu,\n  DropdownMenuPortal,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuLabel,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioGroup,\n  DropdownMenuRadioItem,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuSub,\n  DropdownMenuSubTrigger,\n  DropdownMenuSubContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,aAAa,EACpB,GAAG,OACqD;IACxD,qBAAO,8OAAC,4KAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;AAEA,SAAS,oBAAoB,EAC3B,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;kBAC3B,cAAA,8OAAC,4KAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ;IACC,qBACE,8OAAC,4KAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D;IAChE,qBACE,8OAAC,4KAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;AAEA,SAAS,uBAAuB,EAC9B,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,0MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EACvB,GAAG,OACoD;IACvD,qBAAO,8OAAC,4KAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,0NAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 366, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AIntegrity/aintegrity/src/components/dashboard/chat-history.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { MessageSquare, Plus, MoreHorizontal, Trash2, Edit2 } from 'lucide-react'\nimport { Button } from '@/components/ui/button'\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuTrigger,\n} from '@/components/ui/dropdown-menu'\nimport { cn } from '@/lib/utils'\n\ninterface Chat {\n  id: string\n  title: string\n  createdAt: string\n  updatedAt: string\n  messages: Array<{\n    id: string\n    content: string\n    role: string\n    createdAt: string\n  }>\n  _count: {\n    messages: number\n  }\n}\n\nexport function ChatHistory() {\n  const [chats, setChats] = useState<Chat[]>([])\n  const [loading, setLoading] = useState(true)\n  const pathname = usePathname()\n\n  const fetchChats = async () => {\n    try {\n      const response = await fetch('/api/chats')\n      if (response.ok) {\n        const data = await response.json()\n        setChats(data)\n      }\n    } catch (error) {\n      console.error('Error fetching chats:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  useEffect(() => {\n    fetchChats()\n  }, [])\n\n  const deleteChat = async (chatId: string) => {\n    try {\n      const response = await fetch(`/api/chats/${chatId}`, {\n        method: 'DELETE'\n      })\n      if (response.ok) {\n        setChats(chats.filter(chat => chat.id !== chatId))\n      }\n    } catch (error) {\n      console.error('Error deleting chat:', error)\n    }\n  }\n\n  const formatDate = (dateString: string) => {\n    const date = new Date(dateString)\n    const now = new Date()\n    const diffTime = Math.abs(now.getTime() - date.getTime())\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))\n\n    if (diffDays === 1) return 'Today'\n    if (diffDays === 2) return 'Yesterday'\n    if (diffDays <= 7) return `${diffDays - 1} days ago`\n    return date.toLocaleDateString()\n  }\n\n  if (loading) {\n    return (\n      <div className=\"space-y-2\">\n        {[...Array(5)].map((_, i) => (\n          <div key={i} className=\"h-12 bg-gray-700 rounded animate-pulse\" />\n        ))}\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"space-y-2\">\n      <div className=\"flex items-center justify-between mb-4\">\n        <h3 className=\"text-sm font-medium text-gray-300\">Recent Chats</h3>\n        <Button asChild size=\"sm\" variant=\"ghost\">\n          <Link href=\"/dashboard\">\n            <Plus className=\"h-4 w-4\" />\n          </Link>\n        </Button>\n      </div>\n\n      {chats.length === 0 ? (\n        <div className=\"text-center py-8\">\n          <MessageSquare className=\"mx-auto h-8 w-8 text-gray-400 mb-2\" />\n          <p className=\"text-sm text-gray-400\">No chats yet</p>\n          <Button asChild size=\"sm\" className=\"mt-2\">\n            <Link href=\"/dashboard\">Start chatting</Link>\n          </Button>\n        </div>\n      ) : (\n        <div className=\"space-y-1\">\n          {chats.map((chat) => {\n            const isActive = pathname === `/dashboard/chat/${chat.id}`\n            const lastMessage = chat.messages[0]\n            \n            return (\n              <div\n                key={chat.id}\n                className={cn(\n                  'group relative rounded-md transition-colors',\n                  isActive ? 'bg-gray-700' : 'hover:bg-gray-700/50'\n                )}\n              >\n                <Link\n                  href={`/dashboard/chat/${chat.id}`}\n                  className=\"block p-3 pr-10\"\n                >\n                  <div className=\"flex items-start gap-2\">\n                    <MessageSquare className=\"h-4 w-4 text-gray-400 mt-0.5 flex-shrink-0\" />\n                    <div className=\"min-w-0 flex-1\">\n                      <p className=\"text-sm font-medium text-white truncate\">\n                        {chat.title || 'Untitled Chat'}\n                      </p>\n                      {lastMessage && (\n                        <p className=\"text-xs text-gray-400 truncate mt-1\">\n                          {lastMessage.content.slice(0, 50)}...\n                        </p>\n                      )}\n                      <div className=\"flex items-center gap-2 mt-1\">\n                        <span className=\"text-xs text-gray-500\">\n                          {formatDate(chat.updatedAt)}\n                        </span>\n                        <span className=\"text-xs text-gray-500\">\n                          {chat._count.messages} messages\n                        </span>\n                      </div>\n                    </div>\n                  </div>\n                </Link>\n\n                <DropdownMenu>\n                  <DropdownMenuTrigger asChild>\n                    <Button\n                      variant=\"ghost\"\n                      size=\"sm\"\n                      className=\"absolute right-1 top-1 opacity-0 group-hover:opacity-100 h-8 w-8 p-0\"\n                    >\n                      <MoreHorizontal className=\"h-4 w-4\" />\n                    </Button>\n                  </DropdownMenuTrigger>\n                  <DropdownMenuContent align=\"end\">\n                    <DropdownMenuItem>\n                      <Edit2 className=\"mr-2 h-4 w-4\" />\n                      Rename\n                    </DropdownMenuItem>\n                    <DropdownMenuItem\n                      onClick={() => deleteChat(chat.id)}\n                      className=\"text-red-400\"\n                    >\n                      <Trash2 className=\"mr-2 h-4 w-4\" />\n                      Delete\n                    </DropdownMenuItem>\n                  </DropdownMenuContent>\n                </DropdownMenu>\n              </div>\n            )\n          })}\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAMA;AAbA;;;;;;;;;AA+BO,SAAS;IACd,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,aAAa;QACjB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,SAAS;YACX;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACzC,SAAU;YACR,WAAW;QACb;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,aAAa,OAAO;QACxB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,WAAW,EAAE,QAAQ,EAAE;gBACnD,QAAQ;YACV;YACA,IAAI,SAAS,EAAE,EAAE;gBACf,SAAS,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;YAC5C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;QACxC;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,IAAI,KAAK;QACtB,MAAM,MAAM,IAAI;QAChB,MAAM,WAAW,KAAK,GAAG,CAAC,IAAI,OAAO,KAAK,KAAK,OAAO;QACtD,MAAM,WAAW,KAAK,IAAI,CAAC,WAAW,CAAC,OAAO,KAAK,KAAK,EAAE;QAE1D,IAAI,aAAa,GAAG,OAAO;QAC3B,IAAI,aAAa,GAAG,OAAO;QAC3B,IAAI,YAAY,GAAG,OAAO,GAAG,WAAW,EAAE,SAAS,CAAC;QACpD,OAAO,KAAK,kBAAkB;IAChC;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACZ;mBAAI,MAAM;aAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC;oBAAY,WAAU;mBAAb;;;;;;;;;;IAIlB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAoC;;;;;;kCAClD,8OAAC,kIAAA,CAAA,SAAM;wBAAC,OAAO;wBAAC,MAAK;wBAAK,SAAQ;kCAChC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;sCACT,cAAA,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;YAKrB,MAAM,MAAM,KAAK,kBAChB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,wNAAA,CAAA,gBAAa;wBAAC,WAAU;;;;;;kCACzB,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;kCACrC,8OAAC,kIAAA,CAAA,SAAM;wBAAC,OAAO;wBAAC,MAAK;wBAAK,WAAU;kCAClC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;sCAAa;;;;;;;;;;;;;;;;qCAI5B,8OAAC;gBAAI,WAAU;0BACZ,MAAM,GAAG,CAAC,CAAC;oBACV,MAAM,WAAW,aAAa,CAAC,gBAAgB,EAAE,KAAK,EAAE,EAAE;oBAC1D,MAAM,cAAc,KAAK,QAAQ,CAAC,EAAE;oBAEpC,qBACE,8OAAC;wBAEC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+CACA,WAAW,gBAAgB;;0CAG7B,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAM,CAAC,gBAAgB,EAAE,KAAK,EAAE,EAAE;gCAClC,WAAU;0CAEV,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,wNAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;sDACzB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DACV,KAAK,KAAK,IAAI;;;;;;gDAEhB,6BACC,8OAAC;oDAAE,WAAU;;wDACV,YAAY,OAAO,CAAC,KAAK,CAAC,GAAG;wDAAI;;;;;;;8DAGtC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEACb,WAAW,KAAK,SAAS;;;;;;sEAE5B,8OAAC;4DAAK,WAAU;;gEACb,KAAK,MAAM,CAAC,QAAQ;gEAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAOhC,8OAAC,4IAAA,CAAA,eAAY;;kDACX,8OAAC,4IAAA,CAAA,sBAAmB;wCAAC,OAAO;kDAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,WAAU;sDAEV,cAAA,8OAAC,gNAAA,CAAA,iBAAc;gDAAC,WAAU;;;;;;;;;;;;;;;;kDAG9B,8OAAC,4IAAA,CAAA,sBAAmB;wCAAC,OAAM;;0DACzB,8OAAC,4IAAA,CAAA,mBAAgB;;kEACf,8OAAC,kMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGpC,8OAAC,4IAAA,CAAA,mBAAgB;gDACf,SAAS,IAAM,WAAW,KAAK,EAAE;gDACjC,WAAU;;kEAEV,8OAAC,0MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;uBApDpC,KAAK,EAAE;;;;;gBA2DlB;;;;;;;;;;;;AAKV", "debugId": null}}, {"offset": {"line": 720, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AIntegrity/aintegrity/src/components/dashboard/sidebar.tsx"], "sourcesContent": ["'use client'\n\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { MessageSquare, FileText, Plus, Search } from 'lucide-react'\nimport { Button } from '@/components/ui/button'\nimport { cn } from '@/lib/utils'\nimport { ChatHistory } from './chat-history'\n\nconst navigation = [\n  { name: 'Chat', href: '/dashboard', icon: MessageSquare },\n  { name: 'Notes', href: '/dashboard/notes', icon: FileText },\n  { name: 'Search', href: '/dashboard/search', icon: Search },\n]\n\nexport function Sidebar() {\n  const pathname = usePathname()\n\n  return (\n    <div className=\"hidden lg:flex w-64 bg-gray-800 shadow-sm border-r border-gray-700 flex-col\">\n      <div className=\"p-4\">\n        <h1 className=\"text-xl font-bold text-white\">AIntegrity</h1>\n      </div>\n\n      <nav className=\"px-4 space-y-2 mb-6\">\n        {navigation.map((item) => {\n          const isActive = pathname === item.href ||\n            (item.href === '/dashboard' && pathname.startsWith('/dashboard/chat')) ||\n            (item.href === '/dashboard/notes' && pathname.startsWith('/dashboard/notes'))\n          return (\n            <Link\n              key={item.name}\n              href={item.href}\n              className={cn(\n                'flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors',\n                isActive\n                  ? 'bg-gray-700 text-white'\n                  : 'text-gray-300 hover:bg-gray-700 hover:text-white'\n              )}\n            >\n              <item.icon className=\"mr-3 h-5 w-5\" />\n              {item.name}\n            </Link>\n          )\n        })}\n      </nav>\n\n      <div className=\"flex-1 px-4 overflow-y-auto\">\n        <ChatHistory />\n      </div>\n\n      <div className=\"p-4 border-t border-gray-700\">\n        <Button asChild className=\"w-full mb-2\">\n          <Link href=\"/dashboard/notes/new\">\n            <Plus className=\"mr-2 h-4 w-4\" />\n            New Note\n          </Link>\n        </Button>\n        <Button asChild variant=\"outline\" className=\"w-full\">\n          <Link href=\"/dashboard\">\n            <MessageSquare className=\"mr-2 h-4 w-4\" />\n            New Chat\n          </Link>\n        </Button>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAPA;;;;;;;;AASA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAQ,MAAM;QAAc,MAAM,wNAAA,CAAA,gBAAa;IAAC;IACxD;QAAE,MAAM;QAAS,MAAM;QAAoB,MAAM,8MAAA,CAAA,WAAQ;IAAC;IAC1D;QAAE,MAAM;QAAU,MAAM;QAAqB,MAAM,sMAAA,CAAA,SAAM;IAAC;CAC3D;AAEM,SAAS;IACd,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAG,WAAU;8BAA+B;;;;;;;;;;;0BAG/C,8OAAC;gBAAI,WAAU;0BACZ,WAAW,GAAG,CAAC,CAAC;oBACf,MAAM,WAAW,aAAa,KAAK,IAAI,IACpC,KAAK,IAAI,KAAK,gBAAgB,SAAS,UAAU,CAAC,sBAClD,KAAK,IAAI,KAAK,sBAAsB,SAAS,UAAU,CAAC;oBAC3D,qBACE,8OAAC,4JAAA,CAAA,UAAI;wBAEH,MAAM,KAAK,IAAI;wBACf,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gFACA,WACI,2BACA;;0CAGN,8OAAC,KAAK,IAAI;gCAAC,WAAU;;;;;;4BACpB,KAAK,IAAI;;uBAVL,KAAK,IAAI;;;;;gBAapB;;;;;;0BAGF,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,kJAAA,CAAA,cAAW;;;;;;;;;;0BAGd,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,kIAAA,CAAA,SAAM;wBAAC,OAAO;wBAAC,WAAU;kCACxB,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;;8CACT,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;kCAIrC,8OAAC,kIAAA,CAAA,SAAM;wBAAC,OAAO;wBAAC,SAAQ;wBAAU,WAAU;kCAC1C,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;;8CACT,8OAAC,wNAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;;;;;;;AAOtD", "debugId": null}}, {"offset": {"line": 891, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AIntegrity/aintegrity/src/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Avatar({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Root>) {\n  return (\n    <AvatarPrimitive.Root\n      data-slot=\"avatar\"\n      className={cn(\n        \"relative flex size-8 shrink-0 overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarImage({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Image>) {\n  return (\n    <AvatarPrimitive.Image\n      data-slot=\"avatar-image\"\n      className={cn(\"aspect-square size-full\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarFallback({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\n  return (\n    <AvatarPrimitive.Fallback\n      data-slot=\"avatar-fallback\"\n      className={cn(\n        \"bg-muted flex size-full items-center justify-center rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Avatar, AvatarImage, AvatarFallback }\n"], "names": [], "mappings": ";;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,8OAAC,kKAAA,CAAA,WAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oEACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 943, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AIntegrity/aintegrity/src/components/theme-toggle.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Moon, Sun } from 'lucide-react'\nimport { Button } from '@/components/ui/button'\n\nexport function ThemeToggle() {\n  const [isDark, setIsDark] = useState(true)\n\n  useEffect(() => {\n    // Check if user has a preference stored\n    const stored = localStorage.getItem('theme')\n    if (stored) {\n      setIsDark(stored === 'dark')\n      document.documentElement.className = stored\n    }\n  }, [])\n\n  const toggleTheme = () => {\n    const newTheme = isDark ? 'light' : 'dark'\n    setIsDark(!isDark)\n    document.documentElement.className = newTheme\n    localStorage.setItem('theme', newTheme)\n  }\n\n  return (\n    <Button\n      variant=\"ghost\"\n      size=\"sm\"\n      onClick={toggleTheme}\n      className=\"w-9 h-9 p-0\"\n    >\n      {isDark ? (\n        <Sun className=\"h-4 w-4\" />\n      ) : (\n        <Moon className=\"h-4 w-4\" />\n      )}\n      <span className=\"sr-only\">Toggle theme</span>\n    </Button>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAJA;;;;;AAMO,SAAS;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,wCAAwC;QACxC,MAAM,SAAS,aAAa,OAAO,CAAC;QACpC,IAAI,QAAQ;YACV,UAAU,WAAW;YACrB,SAAS,eAAe,CAAC,SAAS,GAAG;QACvC;IACF,GAAG,EAAE;IAEL,MAAM,cAAc;QAClB,MAAM,WAAW,SAAS,UAAU;QACpC,UAAU,CAAC;QACX,SAAS,eAAe,CAAC,SAAS,GAAG;QACrC,aAAa,OAAO,CAAC,SAAS;IAChC;IAEA,qBACE,8OAAC,kIAAA,CAAA,SAAM;QACL,SAAQ;QACR,MAAK;QACL,SAAS;QACT,WAAU;;YAET,uBACC,8OAAC,gMAAA,CAAA,MAAG;gBAAC,WAAU;;;;;qCAEf,8OAAC,kMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;0BAElB,8OAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC", "debugId": null}}, {"offset": {"line": 1012, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AIntegrity/aintegrity/src/components/providers/mobile-nav-provider.tsx"], "sourcesContent": ["'use client'\n\nimport { createContext, useContext, useState, ReactNode } from 'react'\n\ninterface MobileNavContextType {\n  isMobileNavOpen: boolean\n  setIsMobileNavOpen: (open: boolean) => void\n  toggleMobileNav: () => void\n}\n\nconst MobileNavContext = createContext<MobileNavContextType | undefined>(undefined)\n\nexport function MobileNavProvider({ children }: { children: ReactNode }) {\n  const [isMobileNavOpen, setIsMobileNavOpen] = useState(false)\n\n  const toggleMobileNav = () => {\n    setIsMobileNavOpen(!isMobileNavOpen)\n  }\n\n  return (\n    <MobileNavContext.Provider\n      value={{\n        isMobileNavOpen,\n        setIsMobileNavOpen,\n        toggleMobileNav,\n      }}\n    >\n      {children}\n    </MobileNavContext.Provider>\n  )\n}\n\nexport function useMobileNav() {\n  const context = useContext(MobileNavContext)\n  if (context === undefined) {\n    throw new Error('useMobileNav must be used within a MobileNavProvider')\n  }\n  return context\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAUA,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAoC;AAElE,SAAS,kBAAkB,EAAE,QAAQ,EAA2B;IACrE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,MAAM,kBAAkB;QACtB,mBAAmB,CAAC;IACtB;IAEA,qBACE,8OAAC,iBAAiB,QAAQ;QACxB,OAAO;YACL;YACA;YACA;QACF;kBAEC;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1053, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AIntegrity/aintegrity/src/components/dashboard/header.tsx"], "sourcesContent": ["'use client'\n\nimport { signOut, useSession } from 'next-auth/react'\nimport { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'\nimport { Button } from '@/components/ui/button'\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuTrigger,\n} from '@/components/ui/dropdown-menu'\nimport { LogOut, User, Menu } from 'lucide-react'\nimport { ThemeToggle } from '@/components/theme-toggle'\nimport { useMobileNav } from '@/components/providers/mobile-nav-provider'\n\nexport function Header() {\n  const { data: session } = useSession()\n  const { toggleMobileNav } = useMobileNav()\n\n  const handleSignOut = () => {\n    signOut({ callbackUrl: '/auth/signin' })\n  }\n\n  return (\n    <header className=\"bg-gray-800 shadow-sm border-b border-gray-700 px-4 lg:px-6 py-4\">\n      <div className=\"flex items-center justify-between\">\n        <div className=\"flex items-center gap-3\">\n          {/* Mobile menu button */}\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            onClick={toggleMobileNav}\n            className=\"lg:hidden text-gray-300 hover:text-white\"\n          >\n            <Menu className=\"h-5 w-5\" />\n          </Button>\n          <h2 className=\"text-lg font-semibold text-white\">Dashboard</h2>\n        </div>\n\n        <div className=\"flex items-center gap-2\">\n          <ThemeToggle />\n          <DropdownMenu>\n          <DropdownMenuTrigger asChild>\n            <Button variant=\"ghost\" className=\"relative h-8 w-8 rounded-full\">\n              <Avatar className=\"h-8 w-8\">\n                <AvatarImage src={session?.user?.image || ''} alt={session?.user?.name || ''} />\n                <AvatarFallback>\n                  {session?.user?.name?.charAt(0) || <User className=\"h-4 w-4\" />}\n                </AvatarFallback>\n              </Avatar>\n            </Button>\n          </DropdownMenuTrigger>\n          <DropdownMenuContent className=\"w-56\" align=\"end\" forceMount>\n            <DropdownMenuItem className=\"flex flex-col items-start\">\n              <div className=\"font-medium\">{session?.user?.name}</div>\n              <div className=\"text-xs text-muted-foreground\">{session?.user?.email}</div>\n            </DropdownMenuItem>\n            <DropdownMenuItem onClick={handleSignOut}>\n              <LogOut className=\"mr-2 h-4 w-4\" />\n              Sign out\n            </DropdownMenuItem>\n          </DropdownMenuContent>\n        </DropdownMenu>\n        </div>\n      </div>\n    </header>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAMA;AAAA;AAAA;AACA;AACA;AAbA;;;;;;;;;AAeO,SAAS;IACd,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IACnC,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,4JAAA,CAAA,eAAY,AAAD;IAEvC,MAAM,gBAAgB;QACpB,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD,EAAE;YAAE,aAAa;QAAe;IACxC;IAEA,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS;4BACT,WAAU;sCAEV,cAAA,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;sCAElB,8OAAC;4BAAG,WAAU;sCAAmC;;;;;;;;;;;;8BAGnD,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,qIAAA,CAAA,cAAW;;;;;sCACZ,8OAAC,4IAAA,CAAA,eAAY;;8CACb,8OAAC,4IAAA,CAAA,sBAAmB;oCAAC,OAAO;8CAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAQ,WAAU;kDAChC,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CAAC,WAAU;;8DAChB,8OAAC,kIAAA,CAAA,cAAW;oDAAC,KAAK,SAAS,MAAM,SAAS;oDAAI,KAAK,SAAS,MAAM,QAAQ;;;;;;8DAC1E,8OAAC,kIAAA,CAAA,iBAAc;8DACZ,SAAS,MAAM,MAAM,OAAO,oBAAM,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAK3D,8OAAC,4IAAA,CAAA,sBAAmB;oCAAC,WAAU;oCAAO,OAAM;oCAAM,UAAU;;sDAC1D,8OAAC,4IAAA,CAAA,mBAAgB;4CAAC,WAAU;;8DAC1B,8OAAC;oDAAI,WAAU;8DAAe,SAAS,MAAM;;;;;;8DAC7C,8OAAC;oDAAI,WAAU;8DAAiC,SAAS,MAAM;;;;;;;;;;;;sDAEjE,8OAAC,4IAAA,CAAA,mBAAgB;4CAAC,SAAS;;8DACzB,8OAAC,0MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASjD", "debugId": null}}, {"offset": {"line": 1260, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AIntegrity/aintegrity/src/components/dashboard/mobile-sidebar.tsx"], "sourcesContent": ["'use client'\n\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { MessageSquare, FileText, Plus, X, Search } from 'lucide-react'\nimport { Button } from '@/components/ui/button'\nimport { cn } from '@/lib/utils'\nimport { useMobileNav } from '@/components/providers/mobile-nav-provider'\nimport { useEffect } from 'react'\n\nconst navigation = [\n  { name: 'Chat', href: '/dashboard', icon: MessageSquare },\n  { name: 'Notes', href: '/dashboard/notes', icon: FileText },\n  { name: 'Search', href: '/dashboard/search', icon: Search },\n]\n\nexport function MobileSidebar() {\n  const pathname = usePathname()\n  const { isMobileNavOpen, setIsMobileNavOpen } = useMobileNav()\n\n  // Close mobile nav when route changes\n  useEffect(() => {\n    setIsMobileNavOpen(false)\n  }, [pathname, setIsMobileNavOpen])\n\n  // Prevent body scroll when mobile nav is open\n  useEffect(() => {\n    if (isMobileNavOpen) {\n      document.body.style.overflow = 'hidden'\n    } else {\n      document.body.style.overflow = 'unset'\n    }\n\n    return () => {\n      document.body.style.overflow = 'unset'\n    }\n  }, [isMobileNavOpen])\n\n  if (!isMobileNavOpen) return null\n\n  return (\n    <>\n      {/* Backdrop */}\n      <div\n        className=\"fixed inset-0 z-40 bg-black/50 lg:hidden\"\n        onClick={() => setIsMobileNavOpen(false)}\n      />\n      \n      {/* Mobile Sidebar */}\n      <div className=\"fixed inset-y-0 left-0 z-50 w-64 bg-gray-800 shadow-xl border-r border-gray-700 flex flex-col lg:hidden\">\n        {/* Header with close button */}\n        <div className=\"flex items-center justify-between p-4 border-b border-gray-700\">\n          <h1 className=\"text-xl font-bold text-white\">AIntegrity</h1>\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            onClick={() => setIsMobileNavOpen(false)}\n            className=\"text-gray-300 hover:text-white\"\n          >\n            <X className=\"h-5 w-5\" />\n          </Button>\n        </div>\n        \n        {/* Navigation */}\n        <nav className=\"flex-1 px-4 py-4 space-y-2\">\n          {navigation.map((item) => {\n            const isActive = pathname === item.href\n            return (\n              <Link\n                key={item.name}\n                href={item.href}\n                className={cn(\n                  'flex items-center px-3 py-3 text-sm font-medium rounded-md transition-colors',\n                  isActive\n                    ? 'bg-gray-700 text-white'\n                    : 'text-gray-300 hover:bg-gray-700 hover:text-white'\n                )}\n              >\n                <item.icon className=\"mr-3 h-5 w-5\" />\n                {item.name}\n              </Link>\n            )\n          })}\n        </nav>\n\n        {/* New Note Button */}\n        <div className=\"p-4 border-t border-gray-700\">\n          <Button asChild className=\"w-full\">\n            <Link href=\"/dashboard/notes/new\">\n              <Plus className=\"mr-2 h-4 w-4\" />\n              New Note\n            </Link>\n          </Button>\n        </div>\n      </div>\n    </>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AARA;;;;;;;;;AAUA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAQ,MAAM;QAAc,MAAM,wNAAA,CAAA,gBAAa;IAAC;IACxD;QAAE,MAAM;QAAS,MAAM;QAAoB,MAAM,8MAAA,CAAA,WAAQ;IAAC;IAC1D;QAAE,MAAM;QAAU,MAAM;QAAqB,MAAM,sMAAA,CAAA,SAAM;IAAC;CAC3D;AAEM,SAAS;IACd,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,eAAe,EAAE,kBAAkB,EAAE,GAAG,CAAA,GAAA,4JAAA,CAAA,eAAY,AAAD;IAE3D,sCAAsC;IACtC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,mBAAmB;IACrB,GAAG;QAAC;QAAU;KAAmB;IAEjC,8CAA8C;IAC9C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,iBAAiB;YACnB,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC,OAAO;YACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC;QAEA,OAAO;YACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC;IACF,GAAG;QAAC;KAAgB;IAEpB,IAAI,CAAC,iBAAiB,OAAO;IAE7B,qBACE;;0BAEE,8OAAC;gBACC,WAAU;gBACV,SAAS,IAAM,mBAAmB;;;;;;0BAIpC,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA+B;;;;;;0CAC7C,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,mBAAmB;gCAClC,WAAU;0CAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAKjB,8OAAC;wBAAI,WAAU;kCACZ,WAAW,GAAG,CAAC,CAAC;4BACf,MAAM,WAAW,aAAa,KAAK,IAAI;4BACvC,qBACE,8OAAC,4JAAA,CAAA,UAAI;gCAEH,MAAM,KAAK,IAAI;gCACf,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gFACA,WACI,2BACA;;kDAGN,8OAAC,KAAK,IAAI;wCAAC,WAAU;;;;;;oCACpB,KAAK,IAAI;;+BAVL,KAAK,IAAI;;;;;wBAapB;;;;;;kCAIF,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;4BAAC,OAAO;4BAAC,WAAU;sCACxB,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;;kDACT,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;AAQ/C", "debugId": null}}]}