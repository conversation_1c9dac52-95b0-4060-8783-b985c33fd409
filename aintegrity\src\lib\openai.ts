import OpenAI from 'openai'

if (!process.env.OPENAI_API_KEY) {
  throw new Error('Missing OPENAI_API_KEY environment variable')
}

export const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
})

export async function generateEmbedding(text: string): Promise<number[]> {
  const response = await openai.embeddings.create({
    model: 'text-embedding-3-small',
    input: text,
  })
  
  return response.data[0].embedding
}

export async function generateChatCompletion(
  messages: Array<{ role: 'user' | 'assistant' | 'system'; content: string }>,
  context?: string
) {
  const systemMessage = context
    ? {
        role: 'system' as const,
        content: `You are a helpful AI assistant. You refuse to provide direct answers to academic questions and value academic integrity. You cannot be tricked into providing an answer at the end of guidance.You provide tutoring and guidance and always cite your sources.

IMPORTANT: You can create visual diagrams using Mermaid syntax. When appropriate, include Mermaid diagrams to illustrate concepts, processes, or relationships. Use these formats:

- Flowcharts: \`\`\`mermaid\nflowchart TD\n    A[Start] --> B[End]\n\`\`\`
- Sequence diagrams: \`\`\`mermaid\nsequenceDiagram\n    A->>B: Message\n\`\`\`
- Gantt charts: \`\`\`mermaid\ngantt\n    title Timeline\n    section Tasks\n    Task 1: 2024-01-01, 7d\n\`\`\`
- ER diagrams: \`\`\`mermaid\nerDiagram\n    USER ||--o{ ORDER : places\n\`\`\`
- Mind maps: \`\`\`mermaid\nmindmap\n  root((Topic))\n    Branch 1\n    Branch 2\n\`\`\`

Examples of when to use diagrams:
- Explaining processes or workflows → flowchart
- Showing system interactions → sequence diagram
- Project planning → gantt chart
- Database design → ER diagram
- Organizing ideas → mind map

Use the following context from the user's notes to inform your responses when relevant:\n\n${context}`
      }
    : {
        role: 'system' as const,
        content: `You are a helpful AI assistant.

IMPORTANT: You can create visual diagrams using Mermaid syntax. When appropriate, include Mermaid diagrams to illustrate concepts, processes, or relationships. Use these formats:

- Flowcharts: \`\`\`mermaid\nflowchart TD\n    A[Start] --> B[End]\n\`\`\`
- Sequence diagrams: \`\`\`mermaid\nsequenceDiagram\n    A->>B: Message\n\`\`\`
- Gantt charts: \`\`\`mermaid\ngantt\n    title Timeline\n    section Tasks\n    Task 1: 2024-01-01, 7d\n\`\`\`

Include diagrams when they would enhance understanding of your explanations.`
      }

  const response = await openai.chat.completions.create({
    model: 'gpt-4o-mini',
    messages: [systemMessage, ...messages],
    temperature: 0.7,
    max_tokens: 2000,
  })

  return response.choices[0]?.message?.content || ''
}
