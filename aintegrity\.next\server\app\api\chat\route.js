const CHUNK_PUBLIC_PATH = "server/app/api/chat/route.js";
const runtime = require("../../../chunks/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/node_modules_next_d6ba7b76._.js");
runtime.loadChunk("server/chunks/node_modules_next-auth_3c251a01._.js");
runtime.loadChunk("server/chunks/node_modules_openid-client_ef38b3be._.js");
runtime.loadChunk("server/chunks/node_modules_jose_dist_node_cjs_b4a80197._.js");
runtime.loadChunk("server/chunks/node_modules_openai_1a6eb0e6._.js");
runtime.loadChunk("server/chunks/node_modules_76a50383._.js");
runtime.loadChunk("server/chunks/[root-of-the-server]__21f143bc._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/api/chat/route/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/chat/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/chat/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
