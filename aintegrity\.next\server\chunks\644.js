"use strict";exports.id=644,exports.ids=[644],exports.modules={163:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unstable_rethrow",{enumerable:!0,get:function(){return r}});let r=n(71042).unstable_rethrow;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},363:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]])},2030:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isNavigatingToNewRootLayout",{enumerable:!0,get:function(){return function e(t,n){let r=t[0],o=n[0];if(Array.isArray(r)&&Array.isArray(o)){if(r[0]!==o[0]||r[2]!==o[2])return!0}else if(r!==o)return!0;if(t[4])return!n[4];if(n[4])return!0;let l=Object.values(t[1])[0],a=Object.values(n[1])[0];return!l||!a||e(l,a)}}}),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2255:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"pathHasPrefix",{enumerable:!0,get:function(){return o}});let r=n(19169);function o(e,t){if("string"!=typeof e)return!1;let{pathname:n}=(0,r.parsePath)(e);return n===t||n.startsWith(t+"/")}},5144:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"PromiseQueue",{enumerable:!0,get:function(){return c}});let r=n(51550),o=n(59656);var l=o._("_maxConcurrency"),a=o._("_runningCount"),i=o._("_queue"),u=o._("_processNext");class c{enqueue(e){let t,n,o=new Promise((e,r)=>{t=e,n=r}),l=async()=>{try{r._(this,a)[a]++;let n=await e();t(n)}catch(e){n(e)}finally{r._(this,a)[a]--,r._(this,u)[u]()}};return r._(this,i)[i].push({promiseFn:o,task:l}),r._(this,u)[u](),o}bump(e){let t=r._(this,i)[i].findIndex(t=>t.promiseFn===e);if(t>-1){let e=r._(this,i)[i].splice(t,1)[0];r._(this,i)[i].unshift(e),r._(this,u)[u](!0)}}constructor(e=5){Object.defineProperty(this,u,{value:s}),Object.defineProperty(this,l,{writable:!0,value:void 0}),Object.defineProperty(this,a,{writable:!0,value:void 0}),Object.defineProperty(this,i,{writable:!0,value:void 0}),r._(this,l)[l]=e,r._(this,a)[a]=0,r._(this,i)[i]=[]}}function s(e){if(void 0===e&&(e=!1),(r._(this,a)[a]<r._(this,l)[l]||e)&&r._(this,i)[i].length>0){var t;null==(t=r._(this,i)[i].shift())||t.task()}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5334:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{DYNAMIC_STALETIME_MS:function(){return d},STATIC_STALETIME_MS:function(){return p},createSeededPrefetchCacheEntry:function(){return c},getOrCreatePrefetchCacheEntry:function(){return u},prunePrefetchCache:function(){return f}});let r=n(59008),o=n(59154),l=n(75076);function a(e,t,n){let r=e.pathname;return(t&&(r+=e.search),n)?""+n+"%"+r:r}function i(e,t,n){return a(e,t===o.PrefetchKind.FULL,n)}function u(e){let{url:t,nextUrl:n,tree:r,prefetchCache:l,kind:i,allowAliasing:u=!0}=e,c=function(e,t,n,r,l){for(let i of(void 0===t&&(t=o.PrefetchKind.TEMPORARY),[n,null])){let n=a(e,!0,i),u=a(e,!1,i),c=e.search?n:u,s=r.get(c);if(s&&l){if(s.url.pathname===e.pathname&&s.url.search!==e.search)return{...s,aliased:!0};return s}let f=r.get(u);if(l&&e.search&&t!==o.PrefetchKind.FULL&&f&&!f.key.includes("%"))return{...f,aliased:!0}}if(t!==o.PrefetchKind.FULL&&l){for(let t of r.values())if(t.url.pathname===e.pathname&&!t.key.includes("%"))return{...t,aliased:!0}}}(t,i,n,l,u);return c?(c.status=h(c),c.kind!==o.PrefetchKind.FULL&&i===o.PrefetchKind.FULL&&c.data.then(e=>{if(!(Array.isArray(e.flightData)&&e.flightData.some(e=>e.isRootRender&&null!==e.seedData)))return s({tree:r,url:t,nextUrl:n,prefetchCache:l,kind:null!=i?i:o.PrefetchKind.TEMPORARY})}),i&&c.kind===o.PrefetchKind.TEMPORARY&&(c.kind=i),c):s({tree:r,url:t,nextUrl:n,prefetchCache:l,kind:i||o.PrefetchKind.TEMPORARY})}function c(e){let{nextUrl:t,tree:n,prefetchCache:r,url:l,data:a,kind:u}=e,c=a.couldBeIntercepted?i(l,u,t):i(l,u),s={treeAtTimeOfPrefetch:n,data:Promise.resolve(a),kind:u,prefetchTime:Date.now(),lastUsedTime:Date.now(),staleTime:-1,key:c,status:o.PrefetchCacheEntryStatus.fresh,url:l};return r.set(c,s),s}function s(e){let{url:t,kind:n,tree:a,nextUrl:u,prefetchCache:c}=e,s=i(t,n),f=l.prefetchQueue.enqueue(()=>(0,r.fetchServerResponse)(t,{flightRouterState:a,nextUrl:u,prefetchKind:n}).then(e=>{let n;if(e.couldBeIntercepted&&(n=function(e){let{url:t,nextUrl:n,prefetchCache:r,existingCacheKey:o}=e,l=r.get(o);if(!l)return;let a=i(t,l.kind,n);return r.set(a,{...l,key:a}),r.delete(o),a}({url:t,existingCacheKey:s,nextUrl:u,prefetchCache:c})),e.prerendered){let t=c.get(null!=n?n:s);t&&(t.kind=o.PrefetchKind.FULL,-1!==e.staleTime&&(t.staleTime=e.staleTime))}return e})),d={treeAtTimeOfPrefetch:a,data:f,kind:n,prefetchTime:Date.now(),lastUsedTime:null,staleTime:-1,key:s,status:o.PrefetchCacheEntryStatus.fresh,url:t};return c.set(s,d),d}function f(e){for(let[t,n]of e)h(n)===o.PrefetchCacheEntryStatus.expired&&e.delete(t)}let d=1e3*Number("0"),p=1e3*Number("300");function h(e){let{kind:t,prefetchTime:n,lastUsedTime:r,staleTime:l}=e;return -1!==l?Date.now()<n+l?o.PrefetchCacheEntryStatus.fresh:o.PrefetchCacheEntryStatus.stale:Date.now()<(null!=r?r:n)+d?r?o.PrefetchCacheEntryStatus.reusable:o.PrefetchCacheEntryStatus.fresh:t===o.PrefetchKind.AUTO&&Date.now()<n+p?o.PrefetchCacheEntryStatus.stale:t===o.PrefetchKind.FULL&&Date.now()<n+p?o.PrefetchCacheEntryStatus.reusable:o.PrefetchCacheEntryStatus.expired}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6361:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"assignLocation",{enumerable:!0,get:function(){return o}});let r=n(96127);function o(e,t){if(e.startsWith(".")){let n=t.origin+t.pathname;return new URL((n.endsWith("/")?n:n+"/")+e)}return new URL((0,r.addBasePath)(e),t.href)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8830:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"reducer",{enumerable:!0,get:function(){return r}}),n(59154),n(25232),n(29651),n(28627),n(78866),n(75076),n(97936),n(35429);let r=function(e,t){return e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9707:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{addSearchParamsToPageSegments:function(){return f},handleAliasedPrefetchEntry:function(){return s}});let r=n(83913),o=n(89752),l=n(86770),a=n(57391),i=n(33123),u=n(33898),c=n(59435);function s(e,t,n,s,d){let p,h=t.tree,y=t.cache,v=(0,a.createHrefFromUrl)(s);if("string"==typeof n)return!1;for(let t of n){if(!function e(t){if(!t)return!1;let n=t[2];if(t[3])return!0;for(let t in n)if(e(n[t]))return!0;return!1}(t.seedData))continue;let n=t.tree;n=f(n,Object.fromEntries(s.searchParams));let{seedData:a,isRootRender:c,pathToSegment:d}=t,m=["",...d];n=f(n,Object.fromEntries(s.searchParams));let g=(0,l.applyRouterStatePatchToTree)(m,h,n,v),b=(0,o.createEmptyCacheNode)();if(c&&a){let t=a[1];b.loading=a[3],b.rsc=t,function e(t,n,o,l,a){if(0!==Object.keys(l[1]).length)for(let u in l[1]){let c,s=l[1][u],f=s[0],d=(0,i.createRouterCacheKey)(f),p=null!==a&&void 0!==a[2][u]?a[2][u]:null;if(null!==p){let e=p[1],n=p[3];c={lazyData:null,rsc:f.includes(r.PAGE_SEGMENT_KEY)?null:e,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:n,navigatedAt:t}}else c={lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1};let h=n.parallelRoutes.get(u);h?h.set(d,c):n.parallelRoutes.set(u,new Map([[d,c]])),e(t,c,o,s,p)}}(e,b,y,n,a)}else b.rsc=y.rsc,b.prefetchRsc=y.prefetchRsc,b.loading=y.loading,b.parallelRoutes=new Map(y.parallelRoutes),(0,u.fillCacheWithNewSubTreeDataButOnlyLoading)(e,b,y,t);g&&(h=g,y=b,p=!0)}return!!p&&(d.patchedTree=h,d.cache=y,d.canonicalUrl=v,d.hashFragment=s.hash,(0,c.handleMutable)(t,d))}function f(e,t){let[n,o,...l]=e;if(n.includes(r.PAGE_SEGMENT_KEY))return[(0,r.addSearchParamsIfPageSegment)(n,t),o,...l];let a={};for(let[e,n]of Object.entries(o))a[e]=f(n,t);return[n,a,...l]}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},10022:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},11096:(e,t,n)=>{n.d(t,{H4:()=>P,_V:()=>E,bL:()=>_});var r=n(43210),o=n(11273),l=n(13495),a=n(66156),i=n(14163),u=n(57379);function c(){return()=>{}}var s=n(60687),f="Avatar",[d,p]=(0,o.A)(f),[h,y]=d(f),v=r.forwardRef((e,t)=>{let{__scopeAvatar:n,...o}=e,[l,a]=r.useState("idle");return(0,s.jsx)(h,{scope:n,imageLoadingStatus:l,onImageLoadingStatusChange:a,children:(0,s.jsx)(i.sG.span,{...o,ref:t})})});v.displayName=f;var m="AvatarImage",g=r.forwardRef((e,t)=>{let{__scopeAvatar:n,src:o,onLoadingStatusChange:f=()=>{},...d}=e,p=y(m,n),h=function(e,{referrerPolicy:t,crossOrigin:n}){let o=(0,u.useSyncExternalStore)(c,()=>!0,()=>!1),l=r.useRef(null),i=o?(l.current||(l.current=new window.Image),l.current):null,[s,f]=r.useState(()=>R(i,e));return(0,a.N)(()=>{f(R(i,e))},[i,e]),(0,a.N)(()=>{let e=e=>()=>{f(e)};if(!i)return;let r=e("loaded"),o=e("error");return i.addEventListener("load",r),i.addEventListener("error",o),t&&(i.referrerPolicy=t),"string"==typeof n&&(i.crossOrigin=n),()=>{i.removeEventListener("load",r),i.removeEventListener("error",o)}},[i,n,t]),s}(o,d),v=(0,l.c)(e=>{f(e),p.onImageLoadingStatusChange(e)});return(0,a.N)(()=>{"idle"!==h&&v(h)},[h,v]),"loaded"===h?(0,s.jsx)(i.sG.img,{...d,ref:t,src:o}):null});g.displayName=m;var b="AvatarFallback",w=r.forwardRef((e,t)=>{let{__scopeAvatar:n,delayMs:o,...l}=e,a=y(b,n),[u,c]=r.useState(void 0===o);return r.useEffect(()=>{if(void 0!==o){let e=window.setTimeout(()=>c(!0),o);return()=>window.clearTimeout(e)}},[o]),u&&"loaded"!==a.imageLoadingStatus?(0,s.jsx)(i.sG.span,{...l,ref:t}):null});function R(e,t){return e?t?(e.src!==t&&(e.src=t),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}w.displayName=b;var _=v,E=g,P=w},11273:(e,t,n)=>{n.d(t,{A:()=>l});var r=n(43210),o=n(60687);function l(e,t=[]){let n=[],a=()=>{let t=n.map(e=>r.createContext(e));return function(n){let o=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:o}}),[n,o])}};return a.scopeName=e,[function(t,l){let a=r.createContext(l),i=n.length;n=[...n,l];let u=t=>{let{scope:n,children:l,...u}=t,c=n?.[e]?.[i]||a,s=r.useMemo(()=>u,Object.values(u));return(0,o.jsx)(c.Provider,{value:s,children:l})};return u.displayName=t+"Provider",[u,function(n,o){let u=o?.[e]?.[i]||a,c=r.useContext(u);if(c)return c;if(void 0!==l)return l;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return n.scopeName=t.scopeName,n}(a,...t)]}},11860:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},12941:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]])},13495:(e,t,n)=>{n.d(t,{c:()=>o});var r=n(43210);function o(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}},14163:(e,t,n)=>{n.d(t,{hO:()=>u,sG:()=>i});var r=n(43210),o=n(51215),l=n(8730),a=n(60687),i=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=(0,l.TL)(`Primitive.${t}`),o=r.forwardRef((e,r)=>{let{asChild:o,...l}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,a.jsx)(o?n:t,{...l,ref:r})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{});function u(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},14952:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},18179:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("folder-open",[["path",{d:"m6 14 1.5-2.9A2 2 0 0 1 9.24 10H20a2 2 0 0 1 1.94 2.5l-1.54 6a2 2 0 0 1-1.95 1.5H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h3.9a2 2 0 0 1 1.69.9l.81 1.2a2 2 0 0 0 1.67.9H18a2 2 0 0 1 2 2v2",key:"usdka0"}]])},18468:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"invalidateCacheBelowFlightSegmentPath",{enumerable:!0,get:function(){return function e(t,n,l){let a=l.length<=2,[i,u]=l,c=(0,r.createRouterCacheKey)(u),s=n.parallelRoutes.get(i);if(!s)return;let f=t.parallelRoutes.get(i);if(f&&f!==s||(f=new Map(s),t.parallelRoutes.set(i,f)),a)return void f.delete(c);let d=s.get(c),p=f.get(c);p&&d&&(p===d&&(p={lazyData:p.lazyData,rsc:p.rsc,prefetchRsc:p.prefetchRsc,head:p.head,prefetchHead:p.prefetchHead,parallelRoutes:new Map(p.parallelRoutes)},f.set(c,p)),e(p,d,(0,o.getNextFlightSegmentPath)(l)))}}});let r=n(33123),o=n(74007);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},19169:(e,t)=>{function n(e){let t=e.indexOf("#"),n=e.indexOf("?"),r=n>-1&&(t<0||n<t);return r||t>-1?{pathname:e.substring(0,r?n:t),query:r?e.substring(n,t>-1?t:void 0):"",hash:t>-1?e.slice(t):""}:{pathname:e,query:"",hash:""}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parsePath",{enumerable:!0,get:function(){return n}})},21134:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]])},22308:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{addRefreshMarkerToActiveParallelSegments:function(){return function e(t,n){let[r,o,,a]=t;for(let i in r.includes(l.PAGE_SEGMENT_KEY)&&"refresh"!==a&&(t[2]=n,t[3]="refresh"),o)e(o[i],n)}},refreshInactiveParallelSegments:function(){return a}});let r=n(56928),o=n(59008),l=n(83913);async function a(e){let t=new Set;await i({...e,rootTree:e.updatedTree,fetchedSegments:t})}async function i(e){let{navigatedAt:t,state:n,updatedTree:l,updatedCache:a,includeNextUrl:u,fetchedSegments:c,rootTree:s=l,canonicalUrl:f}=e,[,d,p,h]=l,y=[];if(p&&p!==f&&"refresh"===h&&!c.has(p)){c.add(p);let e=(0,o.fetchServerResponse)(new URL(p,location.origin),{flightRouterState:[s[0],s[1],s[2],"refetch"],nextUrl:u?n.nextUrl:null}).then(e=>{let{flightData:n}=e;if("string"!=typeof n)for(let e of n)(0,r.applyFlightData)(t,a,a,e)});y.push(e)}for(let e in d){let r=i({navigatedAt:t,state:n,updatedTree:d[e],updatedCache:a,includeNextUrl:u,fetchedSegments:c,rootTree:s,canonicalUrl:f});y.push(r)}await Promise.all(y)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},24642:(e,t)=>{function n(e){let t=parseInt(e.slice(0,2),16),n=t>>1&63,r=Array(6);for(let e=0;e<6;e++){let t=n>>5-e&1;r[e]=1===t}return{type:1==(t>>7&1)?"use-cache":"server-action",usedArgs:r,hasRestArgs:1==(1&t)}}function r(e,t){let n=Array(e.length);for(let r=0;r<e.length;r++)(r<6&&t.usedArgs[r]||r>=6&&t.hasRestArgs)&&(n[r]=e[r]);return n}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{extractInfoFromServerReferenceId:function(){return n},omitUnusedArgs:function(){return r}})},25232:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{handleExternalUrl:function(){return b},navigateReducer:function(){return function e(t,n){let{url:R,isExternalUrl:_,navigateType:E,shouldScroll:P,allowAliasing:x}=n,O={},{hash:T}=R,j=(0,o.createHrefFromUrl)(R),M="push"===E;if((0,v.prunePrefetchCache)(t.prefetchCache),O.preserveCustomHistoryState=!1,O.pendingPush=M,_)return b(t,O,R.toString(),M);if(document.getElementById("__next-page-redirect"))return b(t,O,j,M);let S=(0,v.getOrCreatePrefetchCacheEntry)({url:R,nextUrl:t.nextUrl,tree:t.tree,prefetchCache:t.prefetchCache,allowAliasing:x}),{treeAtTimeOfPrefetch:C,data:A}=S;return d.prefetchQueue.bump(A),A.then(d=>{let{flightData:v,canonicalUrl:_,postponed:E}=d,x=Date.now(),A=!1;if(S.lastUsedTime||(S.lastUsedTime=x,A=!0),S.aliased){let r=(0,g.handleAliasedPrefetchEntry)(x,t,v,R,O);return!1===r?e(t,{...n,allowAliasing:!1}):r}if("string"==typeof v)return b(t,O,v,M);let N=_?(0,o.createHrefFromUrl)(_):j;if(T&&t.canonicalUrl.split("#",1)[0]===N.split("#",1)[0])return O.onlyHashChange=!0,O.canonicalUrl=N,O.shouldScroll=P,O.hashFragment=T,O.scrollableSegments=[],(0,s.handleMutable)(t,O);let L=t.tree,k=t.cache,D=[];for(let e of v){let{pathToSegment:n,seedData:o,head:s,isHeadPartial:d,isRootRender:v}=e,g=e.tree,_=["",...n],P=(0,a.applyRouterStatePatchToTree)(_,L,g,j);if(null===P&&(P=(0,a.applyRouterStatePatchToTree)(_,C,g,j)),null!==P){if(o&&v&&E){let e=(0,y.startPPRNavigation)(x,k,L,g,o,s,d,!1,D);if(null!==e){if(null===e.route)return b(t,O,j,M);P=e.route;let n=e.node;null!==n&&(O.cache=n);let o=e.dynamicRequestTree;if(null!==o){let n=(0,r.fetchServerResponse)(R,{flightRouterState:o,nextUrl:t.nextUrl});(0,y.listenForDynamicRequest)(e,n)}}else P=g}else{if((0,u.isNavigatingToNewRootLayout)(L,P))return b(t,O,j,M);let r=(0,p.createEmptyCacheNode)(),o=!1;for(let t of(S.status!==c.PrefetchCacheEntryStatus.stale||A?o=(0,f.applyFlightData)(x,k,r,e,S):(o=function(e,t,n,r){let o=!1;for(let l of(e.rsc=t.rsc,e.prefetchRsc=t.prefetchRsc,e.loading=t.loading,e.parallelRoutes=new Map(t.parallelRoutes),w(r).map(e=>[...n,...e])))(0,m.clearCacheNodeDataForSegmentPath)(e,t,l),o=!0;return o}(r,k,n,g),S.lastUsedTime=x),(0,i.shouldHardNavigate)(_,L)?(r.rsc=k.rsc,r.prefetchRsc=k.prefetchRsc,(0,l.invalidateCacheBelowFlightSegmentPath)(r,k,n),O.cache=r):o&&(O.cache=r,k=r),w(g))){let e=[...n,...t];e[e.length-1]!==h.DEFAULT_SEGMENT_KEY&&D.push(e)}}L=P}}return O.patchedTree=L,O.canonicalUrl=N,O.scrollableSegments=D,O.hashFragment=T,O.shouldScroll=P,(0,s.handleMutable)(t,O)},()=>t)}}});let r=n(59008),o=n(57391),l=n(18468),a=n(86770),i=n(65951),u=n(2030),c=n(59154),s=n(59435),f=n(56928),d=n(75076),p=n(89752),h=n(83913),y=n(65956),v=n(5334),m=n(97464),g=n(9707);function b(e,t,n,r){return t.mpaNavigation=!0,t.canonicalUrl=n,t.pendingPush=r,t.scrollableSegments=void 0,(0,s.handleMutable)(e,t)}function w(e){let t=[],[n,r]=e;if(0===Object.keys(r).length)return[[n]];for(let[e,o]of Object.entries(r))for(let r of w(o))""===n?t.push([e,...r]):t.push([n,e,...r]);return t}n(50593),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},25942:(e,t,n)=>{function r(e){return e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removeBasePath",{enumerable:!0,get:function(){return r}}),n(26736),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},26736:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"hasBasePath",{enumerable:!0,get:function(){return o}});let r=n(2255);function o(e){return(0,r.pathHasPrefix)(e,"")}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},28627:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"restoreReducer",{enumerable:!0,get:function(){return l}});let r=n(57391),o=n(70642);function l(e,t){var n;let{url:l,tree:a}=t,i=(0,r.createHrefFromUrl)(l),u=a||e.tree,c=e.cache;return{canonicalUrl:i,pushRef:{pendingPush:!1,mpaNavigation:!1,preserveCustomHistoryState:!0},focusAndScrollRef:e.focusAndScrollRef,cache:c,prefetchCache:e.prefetchCache,tree:u,nextUrl:null!=(n=(0,o.extractPathFromFlightRouterState)(u))?n:l.pathname}}n(65956),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},29651:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"serverPatchReducer",{enumerable:!0,get:function(){return s}});let r=n(57391),o=n(86770),l=n(2030),a=n(25232),i=n(56928),u=n(59435),c=n(89752);function s(e,t){let{serverResponse:{flightData:n,canonicalUrl:s},navigatedAt:f}=t,d={};if(d.preserveCustomHistoryState=!1,"string"==typeof n)return(0,a.handleExternalUrl)(e,d,n,e.pushRef.pendingPush);let p=e.tree,h=e.cache;for(let t of n){let{segmentPath:n,tree:u}=t,y=(0,o.applyRouterStatePatchToTree)(["",...n],p,u,e.canonicalUrl);if(null===y)return e;if((0,l.isNavigatingToNewRootLayout)(p,y))return(0,a.handleExternalUrl)(e,d,e.canonicalUrl,e.pushRef.pendingPush);let v=s?(0,r.createHrefFromUrl)(s):void 0;v&&(d.canonicalUrl=v);let m=(0,c.createEmptyCacheNode)();(0,i.applyFlightData)(f,h,m,t),d.patchedTree=y,d.cache=m,h=m,p=y}return(0,u.handleMutable)(e,d)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},30195:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{formatUrl:function(){return l},formatWithValidation:function(){return i},urlObjectKeys:function(){return a}});let r=n(40740)._(n(76715)),o=/https?|ftp|gopher|file/;function l(e){let{auth:t,hostname:n}=e,l=e.protocol||"",a=e.pathname||"",i=e.hash||"",u=e.query||"",c=!1;t=t?encodeURIComponent(t).replace(/%3A/i,":")+"@":"",e.host?c=t+e.host:n&&(c=t+(~n.indexOf(":")?"["+n+"]":n),e.port&&(c+=":"+e.port)),u&&"object"==typeof u&&(u=String(r.urlQueryToSearchParams(u)));let s=e.search||u&&"?"+u||"";return l&&!l.endsWith(":")&&(l+=":"),e.slashes||(!l||o.test(l))&&!1!==c?(c="//"+(c||""),a&&"/"!==a[0]&&(a="/"+a)):c||(c=""),i&&"#"!==i[0]&&(i="#"+i),s&&"?"!==s[0]&&(s="?"+s),""+l+c+(a=a.replace(/[?#]/g,encodeURIComponent))+(s=s.replace("#","%23"))+i}let a=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function i(e){return l(e)}},32708:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"errorOnce",{enumerable:!0,get:function(){return n}});let n=e=>{}},33898:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{fillCacheWithNewSubTreeData:function(){return u},fillCacheWithNewSubTreeDataButOnlyLoading:function(){return c}});let r=n(34400),o=n(41500),l=n(33123),a=n(83913);function i(e,t,n,i,u,c){let{segmentPath:s,seedData:f,tree:d,head:p}=i,h=t,y=n;for(let t=0;t<s.length;t+=2){let n=s[t],i=s[t+1],v=t===s.length-2,m=(0,l.createRouterCacheKey)(i),g=y.parallelRoutes.get(n);if(!g)continue;let b=h.parallelRoutes.get(n);b&&b!==g||(b=new Map(g),h.parallelRoutes.set(n,b));let w=g.get(m),R=b.get(m);if(v){if(f&&(!R||!R.lazyData||R===w)){let t=f[0],n=f[1],l=f[3];R={lazyData:null,rsc:c||t!==a.PAGE_SEGMENT_KEY?n:null,prefetchRsc:null,head:null,prefetchHead:null,loading:l,parallelRoutes:c&&w?new Map(w.parallelRoutes):new Map,navigatedAt:e},w&&c&&(0,r.invalidateCacheByRouterState)(R,w,d),c&&(0,o.fillLazyItemsTillLeafWithHead)(e,R,w,d,f,p,u),b.set(m,R)}continue}R&&w&&(R===w&&(R={lazyData:R.lazyData,rsc:R.rsc,prefetchRsc:R.prefetchRsc,head:R.head,prefetchHead:R.prefetchHead,parallelRoutes:new Map(R.parallelRoutes),loading:R.loading},b.set(m,R)),h=R,y=w)}}function u(e,t,n,r,o){i(e,t,n,r,o,!0)}function c(e,t,n,r,o){i(e,t,n,r,o,!1)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},34400:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"invalidateCacheByRouterState",{enumerable:!0,get:function(){return o}});let r=n(33123);function o(e,t,n){for(let o in n[1]){let l=n[1][o][0],a=(0,r.createRouterCacheKey)(l),i=t.parallelRoutes.get(o);if(i){let t=new Map(i);t.delete(a),e.parallelRoutes.set(o,t)}}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},35416:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{HTML_LIMITED_BOT_UA_RE:function(){return r.HTML_LIMITED_BOT_UA_RE},HTML_LIMITED_BOT_UA_RE_STRING:function(){return l},getBotType:function(){return u},isBot:function(){return i}});let r=n(95796),o=/Googlebot|Google-PageRenderer|AdsBot-Google|googleweblight|Storebot-Google/i,l=r.HTML_LIMITED_BOT_UA_RE.source;function a(e){return r.HTML_LIMITED_BOT_UA_RE.test(e)}function i(e){return o.test(e)||a(e)}function u(e){return o.test(e)?"dom":a(e)?"html":void 0}},35429:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"serverActionReducer",{enumerable:!0,get:function(){return M}});let r=n(11264),o=n(11448),l=n(91563),a=n(59154),i=n(6361),u=n(57391),c=n(25232),s=n(86770),f=n(2030),d=n(59435),p=n(41500),h=n(89752),y=n(68214),v=n(96493),m=n(22308),g=n(74007),b=n(36875),w=n(97860),R=n(5334),_=n(25942),E=n(26736),P=n(24642);n(50593);let{createFromFetch:x,createTemporaryReferenceSet:O,encodeReply:T}=n(19357);async function j(e,t,n){let a,u,{actionId:c,actionArgs:s}=n,f=O(),d=(0,P.extractInfoFromServerReferenceId)(c),p="use-cache"===d.type?(0,P.omitUnusedArgs)(s,d):s,h=await T(p,{temporaryReferences:f}),y=await fetch("",{method:"POST",headers:{Accept:l.RSC_CONTENT_TYPE_HEADER,[l.ACTION_HEADER]:c,[l.NEXT_ROUTER_STATE_TREE_HEADER]:encodeURIComponent(JSON.stringify(e.tree)),...{},...t?{[l.NEXT_URL]:t}:{}},body:h}),v=y.headers.get("x-action-redirect"),[m,b]=(null==v?void 0:v.split(";"))||[];switch(b){case"push":a=w.RedirectType.push;break;case"replace":a=w.RedirectType.replace;break;default:a=void 0}let R=!!y.headers.get(l.NEXT_IS_PRERENDER_HEADER);try{let e=JSON.parse(y.headers.get("x-action-revalidated")||"[[],0,0]");u={paths:e[0]||[],tag:!!e[1],cookie:e[2]}}catch(e){u={paths:[],tag:!1,cookie:!1}}let _=m?(0,i.assignLocation)(m,new URL(e.canonicalUrl,window.location.href)):void 0,E=y.headers.get("content-type");if(null==E?void 0:E.startsWith(l.RSC_CONTENT_TYPE_HEADER)){let e=await x(Promise.resolve(y),{callServer:r.callServer,findSourceMapURL:o.findSourceMapURL,temporaryReferences:f});return m?{actionFlightData:(0,g.normalizeFlightData)(e.f),redirectLocation:_,redirectType:a,revalidatedParts:u,isPrerender:R}:{actionResult:e.a,actionFlightData:(0,g.normalizeFlightData)(e.f),redirectLocation:_,redirectType:a,revalidatedParts:u,isPrerender:R}}if(y.status>=400)throw Object.defineProperty(Error("text/plain"===E?await y.text():"An unexpected response was received from the server."),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return{redirectLocation:_,redirectType:a,revalidatedParts:u,isPrerender:R}}function M(e,t){let{resolve:n,reject:r}=t,o={},l=e.tree;o.preserveCustomHistoryState=!1;let i=e.nextUrl&&(0,y.hasInterceptionRouteInCurrentTree)(e.tree)?e.nextUrl:null,g=Date.now();return j(e,i,t).then(async y=>{let P,{actionResult:x,actionFlightData:O,redirectLocation:T,redirectType:j,isPrerender:M,revalidatedParts:S}=y;if(T&&(j===w.RedirectType.replace?(e.pushRef.pendingPush=!1,o.pendingPush=!1):(e.pushRef.pendingPush=!0,o.pendingPush=!0),o.canonicalUrl=P=(0,u.createHrefFromUrl)(T,!1)),!O)return(n(x),T)?(0,c.handleExternalUrl)(e,o,T.href,e.pushRef.pendingPush):e;if("string"==typeof O)return n(x),(0,c.handleExternalUrl)(e,o,O,e.pushRef.pendingPush);let C=S.paths.length>0||S.tag||S.cookie;for(let r of O){let{tree:a,seedData:u,head:d,isRootRender:y}=r;if(!y)return console.log("SERVER ACTION APPLY FAILED"),n(x),e;let b=(0,s.applyRouterStatePatchToTree)([""],l,a,P||e.canonicalUrl);if(null===b)return n(x),(0,v.handleSegmentMismatch)(e,t,a);if((0,f.isNavigatingToNewRootLayout)(l,b))return n(x),(0,c.handleExternalUrl)(e,o,P||e.canonicalUrl,e.pushRef.pendingPush);if(null!==u){let t=u[1],n=(0,h.createEmptyCacheNode)();n.rsc=t,n.prefetchRsc=null,n.loading=u[3],(0,p.fillLazyItemsTillLeafWithHead)(g,n,void 0,a,u,d,void 0),o.cache=n,o.prefetchCache=new Map,C&&await (0,m.refreshInactiveParallelSegments)({navigatedAt:g,state:e,updatedTree:b,updatedCache:n,includeNextUrl:!!i,canonicalUrl:o.canonicalUrl||e.canonicalUrl})}o.patchedTree=b,l=b}return T&&P?(C||((0,R.createSeededPrefetchCacheEntry)({url:T,data:{flightData:O,canonicalUrl:void 0,couldBeIntercepted:!1,prerendered:!1,postponed:!1,staleTime:-1},tree:e.tree,prefetchCache:e.prefetchCache,nextUrl:e.nextUrl,kind:M?a.PrefetchKind.FULL:a.PrefetchKind.AUTO}),o.prefetchCache=e.prefetchCache),r((0,b.getRedirectError)((0,E.hasBasePath)(P)?(0,_.removeBasePath)(P):P,j||w.RedirectType.push))):n(x),(0,d.handleMutable)(e,o)},t=>(r(t),e))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},39916:(e,t,n)=>{var r=n(97576);n.o(r,"redirect")&&n.d(t,{redirect:function(){return r.redirect}})},40083:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]])},40591:(e,t,n)=>{n.d(t,{UC:()=>rA,q7:()=>rN,ZL:()=>rC,bL:()=>rM,l9:()=>rS});var r,o,l,a=n(43210),i=n.t(a,2);function u(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}var c=n(98599),s=n(11273),f=n(66156),d=i[" useInsertionEffect ".trim().toString()]||f.N;function p({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[o,l,i]=function({defaultProp:e,onChange:t}){let[n,r]=a.useState(e),o=a.useRef(n),l=a.useRef(t);return d(()=>{l.current=t},[t]),a.useEffect(()=>{o.current!==n&&(l.current?.(n),o.current=n)},[n,o]),[n,r,l]}({defaultProp:t,onChange:n}),u=void 0!==e,c=u?e:o;{let t=a.useRef(void 0!==e);a.useEffect(()=>{let e=t.current;if(e!==u){let t=u?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=u},[u,r])}return[c,a.useCallback(t=>{if(u){let n="function"==typeof t?t(e):t;n!==e&&i.current?.(n)}else l(t)},[u,e,l,i])]}Symbol("RADIX:SYNC_STATE");var h=n(14163),y=n(8730),v=n(60687);function m(e){let t=e+"CollectionProvider",[n,r]=(0,s.A)(t),[o,l]=n(t,{collectionRef:{current:null},itemMap:new Map}),i=e=>{let{scope:t,children:n}=e,r=a.useRef(null),l=a.useRef(new Map).current;return(0,v.jsx)(o,{scope:t,itemMap:l,collectionRef:r,children:n})};i.displayName=t;let u=e+"CollectionSlot",f=(0,y.TL)(u),d=a.forwardRef((e,t)=>{let{scope:n,children:r}=e,o=l(u,n),a=(0,c.s)(t,o.collectionRef);return(0,v.jsx)(f,{ref:a,children:r})});d.displayName=u;let p=e+"CollectionItemSlot",h="data-radix-collection-item",m=(0,y.TL)(p),g=a.forwardRef((e,t)=>{let{scope:n,children:r,...o}=e,i=a.useRef(null),u=(0,c.s)(t,i),s=l(p,n);return a.useEffect(()=>(s.itemMap.set(i,{ref:i,...o}),()=>void s.itemMap.delete(i))),(0,v.jsx)(m,{...{[h]:""},ref:u,children:r})});return g.displayName=p,[{Provider:i,Slot:d,ItemSlot:g},function(t){let n=l(e+"CollectionConsumer",t);return a.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll(`[${h}]`));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},r]}var g=new WeakMap;function b(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let n=function(e,t){let n=e.length,r=w(t),o=r>=0?r:n+r;return o<0||o>=n?-1:o}(e,t);return -1===n?void 0:e[n]}function w(e){return e!=e||0===e?0:Math.trunc(e)}var R=a.createContext(void 0);function _(e){let t=a.useContext(R);return e||t||"ltr"}var E=n(13495),P="dismissableLayer.update",x=a.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),O=a.forwardRef((e,t)=>{let{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:r,onPointerDownOutside:l,onFocusOutside:i,onInteractOutside:s,onDismiss:f,...d}=e,p=a.useContext(x),[y,m]=a.useState(null),g=y?.ownerDocument??globalThis?.document,[,b]=a.useState({}),w=(0,c.s)(t,e=>m(e)),R=Array.from(p.layers),[_]=[...p.layersWithOutsidePointerEventsDisabled].slice(-1),O=R.indexOf(_),M=y?R.indexOf(y):-1,S=p.layersWithOutsidePointerEventsDisabled.size>0,C=M>=O,A=function(e,t=globalThis?.document){let n=(0,E.c)(e),r=a.useRef(!1),o=a.useRef(()=>{});return a.useEffect(()=>{let e=e=>{if(e.target&&!r.current){let r=function(){j("dismissableLayer.pointerDownOutside",n,l,{discrete:!0})},l={originalEvent:e};"touch"===e.pointerType?(t.removeEventListener("click",o.current),o.current=r,t.addEventListener("click",o.current,{once:!0})):r()}else t.removeEventListener("click",o.current);r.current=!1},l=window.setTimeout(()=>{t.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(l),t.removeEventListener("pointerdown",e),t.removeEventListener("click",o.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}(e=>{let t=e.target,n=[...p.branches].some(e=>e.contains(t));C&&!n&&(l?.(e),s?.(e),e.defaultPrevented||f?.())},g),N=function(e,t=globalThis?.document){let n=(0,E.c)(e),r=a.useRef(!1);return a.useEffect(()=>{let e=e=>{e.target&&!r.current&&j("dismissableLayer.focusOutside",n,{originalEvent:e},{discrete:!1})};return t.addEventListener("focusin",e),()=>t.removeEventListener("focusin",e)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}(e=>{let t=e.target;![...p.branches].some(e=>e.contains(t))&&(i?.(e),s?.(e),e.defaultPrevented||f?.())},g);return!function(e,t=globalThis?.document){let n=(0,E.c)(e);a.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{M===p.layers.size-1&&(r?.(e),!e.defaultPrevented&&f&&(e.preventDefault(),f()))},g),a.useEffect(()=>{if(y)return n&&(0===p.layersWithOutsidePointerEventsDisabled.size&&(o=g.body.style.pointerEvents,g.body.style.pointerEvents="none"),p.layersWithOutsidePointerEventsDisabled.add(y)),p.layers.add(y),T(),()=>{n&&1===p.layersWithOutsidePointerEventsDisabled.size&&(g.body.style.pointerEvents=o)}},[y,g,n,p]),a.useEffect(()=>()=>{y&&(p.layers.delete(y),p.layersWithOutsidePointerEventsDisabled.delete(y),T())},[y,p]),a.useEffect(()=>{let e=()=>b({});return document.addEventListener(P,e),()=>document.removeEventListener(P,e)},[]),(0,v.jsx)(h.sG.div,{...d,ref:w,style:{pointerEvents:S?C?"auto":"none":void 0,...e.style},onFocusCapture:u(e.onFocusCapture,N.onFocusCapture),onBlurCapture:u(e.onBlurCapture,N.onBlurCapture),onPointerDownCapture:u(e.onPointerDownCapture,A.onPointerDownCapture)})});function T(){let e=new CustomEvent(P);document.dispatchEvent(e)}function j(e,t,n,{discrete:r}){let o=n.originalEvent.target,l=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?(0,h.hO)(o,l):o.dispatchEvent(l)}O.displayName="DismissableLayer",a.forwardRef((e,t)=>{let n=a.useContext(x),r=a.useRef(null),o=(0,c.s)(t,r);return a.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,v.jsx)(h.sG.div,{...e,ref:o})}).displayName="DismissableLayerBranch";var M=0;function S(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var C="focusScope.autoFocusOnMount",A="focusScope.autoFocusOnUnmount",N={bubbles:!1,cancelable:!0},L=a.forwardRef((e,t)=>{let{loop:n=!1,trapped:r=!1,onMountAutoFocus:o,onUnmountAutoFocus:l,...i}=e,[u,s]=a.useState(null),f=(0,E.c)(o),d=(0,E.c)(l),p=a.useRef(null),y=(0,c.s)(t,e=>s(e)),m=a.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;a.useEffect(()=>{if(r){let e=function(e){if(m.paused||!u)return;let t=e.target;u.contains(t)?p.current=t:I(p.current,{select:!0})},t=function(e){if(m.paused||!u)return;let t=e.relatedTarget;null!==t&&(u.contains(t)||I(p.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&I(u)});return u&&n.observe(u,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[r,u,m.paused]),a.useEffect(()=>{if(u){U.add(m);let e=document.activeElement;if(!u.contains(e)){let t=new CustomEvent(C,N);u.addEventListener(C,f),u.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){let n=document.activeElement;for(let r of e)if(I(r,{select:t}),document.activeElement!==n)return}(k(u).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&I(u))}return()=>{u.removeEventListener(C,f),setTimeout(()=>{let t=new CustomEvent(A,N);u.addEventListener(A,d),u.dispatchEvent(t),t.defaultPrevented||I(e??document.body,{select:!0}),u.removeEventListener(A,d),U.remove(m)},0)}}},[u,f,d,m]);let g=a.useCallback(e=>{if(!n&&!r||m.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,o=document.activeElement;if(t&&o){let t=e.currentTarget,[r,l]=function(e){let t=k(e);return[D(t,e),D(t.reverse(),e)]}(t);r&&l?e.shiftKey||o!==l?e.shiftKey&&o===r&&(e.preventDefault(),n&&I(l,{select:!0})):(e.preventDefault(),n&&I(r,{select:!0})):o===t&&e.preventDefault()}},[n,r,m.paused]);return(0,v.jsx)(h.sG.div,{tabIndex:-1,...i,ref:y,onKeyDown:g})});function k(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function D(e,t){for(let n of e)if(!function(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===t||e!==t);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function I(e,{select:t=!1}={}){if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}L.displayName="FocusScope";var U=function(){let e=[];return{add(t){let n=e[0];t!==n&&n?.pause(),(e=F(e,t)).unshift(t)},remove(t){e=F(e,t),e[0]?.resume()}}}();function F(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}var H=i[" useId ".trim().toString()]||(()=>void 0),K=0;function B(e){let[t,n]=a.useState(H());return(0,f.N)(()=>{e||n(e=>e??String(K++))},[e]),e||(t?`radix-${t}`:"")}let z=["top","right","bottom","left"],W=Math.min,G=Math.max,V=Math.round,X=Math.floor,Y=e=>({x:e,y:e}),q={left:"right",right:"left",bottom:"top",top:"bottom"},$={start:"end",end:"start"};function Z(e,t){return"function"==typeof e?e(t):e}function Q(e){return e.split("-")[0]}function J(e){return e.split("-")[1]}function ee(e){return"x"===e?"y":"x"}function et(e){return"y"===e?"height":"width"}function en(e){return["top","bottom"].includes(Q(e))?"y":"x"}function er(e){return e.replace(/start|end/g,e=>$[e])}function eo(e){return e.replace(/left|right|bottom|top/g,e=>q[e])}function el(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function ea(e){let{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function ei(e,t,n){let r,{reference:o,floating:l}=e,a=en(t),i=ee(en(t)),u=et(i),c=Q(t),s="y"===a,f=o.x+o.width/2-l.width/2,d=o.y+o.height/2-l.height/2,p=o[u]/2-l[u]/2;switch(c){case"top":r={x:f,y:o.y-l.height};break;case"bottom":r={x:f,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:d};break;case"left":r={x:o.x-l.width,y:d};break;default:r={x:o.x,y:o.y}}switch(J(t)){case"start":r[i]-=p*(n&&s?-1:1);break;case"end":r[i]+=p*(n&&s?-1:1)}return r}let eu=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:l=[],platform:a}=n,i=l.filter(Boolean),u=await (null==a.isRTL?void 0:a.isRTL(t)),c=await a.getElementRects({reference:e,floating:t,strategy:o}),{x:s,y:f}=ei(c,r,u),d=r,p={},h=0;for(let n=0;n<i.length;n++){let{name:l,fn:y}=i[n],{x:v,y:m,data:g,reset:b}=await y({x:s,y:f,initialPlacement:r,placement:d,strategy:o,middlewareData:p,rects:c,platform:a,elements:{reference:e,floating:t}});s=null!=v?v:s,f=null!=m?m:f,p={...p,[l]:{...p[l],...g}},b&&h<=50&&(h++,"object"==typeof b&&(b.placement&&(d=b.placement),b.rects&&(c=!0===b.rects?await a.getElementRects({reference:e,floating:t,strategy:o}):b.rects),{x:s,y:f}=ei(c,d,u)),n=-1)}return{x:s,y:f,placement:d,strategy:o,middlewareData:p}};async function ec(e,t){var n;void 0===t&&(t={});let{x:r,y:o,platform:l,rects:a,elements:i,strategy:u}=e,{boundary:c="clippingAncestors",rootBoundary:s="viewport",elementContext:f="floating",altBoundary:d=!1,padding:p=0}=Z(t,e),h=el(p),y=i[d?"floating"===f?"reference":"floating":f],v=ea(await l.getClippingRect({element:null==(n=await (null==l.isElement?void 0:l.isElement(y)))||n?y:y.contextElement||await (null==l.getDocumentElement?void 0:l.getDocumentElement(i.floating)),boundary:c,rootBoundary:s,strategy:u})),m="floating"===f?{x:r,y:o,width:a.floating.width,height:a.floating.height}:a.reference,g=await (null==l.getOffsetParent?void 0:l.getOffsetParent(i.floating)),b=await (null==l.isElement?void 0:l.isElement(g))&&await (null==l.getScale?void 0:l.getScale(g))||{x:1,y:1},w=ea(l.convertOffsetParentRelativeRectToViewportRelativeRect?await l.convertOffsetParentRelativeRectToViewportRelativeRect({elements:i,rect:m,offsetParent:g,strategy:u}):m);return{top:(v.top-w.top+h.top)/b.y,bottom:(w.bottom-v.bottom+h.bottom)/b.y,left:(v.left-w.left+h.left)/b.x,right:(w.right-v.right+h.right)/b.x}}function es(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function ef(e){return z.some(t=>e[t]>=0)}async function ed(e,t){let{placement:n,platform:r,elements:o}=e,l=await (null==r.isRTL?void 0:r.isRTL(o.floating)),a=Q(n),i=J(n),u="y"===en(n),c=["left","top"].includes(a)?-1:1,s=l&&u?-1:1,f=Z(t,e),{mainAxis:d,crossAxis:p,alignmentAxis:h}="number"==typeof f?{mainAxis:f,crossAxis:0,alignmentAxis:null}:{mainAxis:f.mainAxis||0,crossAxis:f.crossAxis||0,alignmentAxis:f.alignmentAxis};return i&&"number"==typeof h&&(p="end"===i?-1*h:h),u?{x:p*s,y:d*c}:{x:d*c,y:p*s}}function ep(){return"undefined"!=typeof window}function eh(e){return em(e)?(e.nodeName||"").toLowerCase():"#document"}function ey(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function ev(e){var t;return null==(t=(em(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function em(e){return!!ep()&&(e instanceof Node||e instanceof ey(e).Node)}function eg(e){return!!ep()&&(e instanceof Element||e instanceof ey(e).Element)}function eb(e){return!!ep()&&(e instanceof HTMLElement||e instanceof ey(e).HTMLElement)}function ew(e){return!!ep()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof ey(e).ShadowRoot)}function eR(e){let{overflow:t,overflowX:n,overflowY:r,display:o}=eO(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function e_(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function eE(e){let t=eP(),n=eg(e)?eO(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(n.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(n.contain||"").includes(e))}function eP(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function ex(e){return["html","body","#document"].includes(eh(e))}function eO(e){return ey(e).getComputedStyle(e)}function eT(e){return eg(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function ej(e){if("html"===eh(e))return e;let t=e.assignedSlot||e.parentNode||ew(e)&&e.host||ev(e);return ew(t)?t.host:t}function eM(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let o=function e(t){let n=ej(t);return ex(n)?t.ownerDocument?t.ownerDocument.body:t.body:eb(n)&&eR(n)?n:e(n)}(e),l=o===(null==(r=e.ownerDocument)?void 0:r.body),a=ey(o);if(l){let e=eS(a);return t.concat(a,a.visualViewport||[],eR(o)?o:[],e&&n?eM(e):[])}return t.concat(o,eM(o,[],n))}function eS(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function eC(e){let t=eO(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,o=eb(e),l=o?e.offsetWidth:n,a=o?e.offsetHeight:r,i=V(n)!==l||V(r)!==a;return i&&(n=l,r=a),{width:n,height:r,$:i}}function eA(e){return eg(e)?e:e.contextElement}function eN(e){let t=eA(e);if(!eb(t))return Y(1);let n=t.getBoundingClientRect(),{width:r,height:o,$:l}=eC(t),a=(l?V(n.width):n.width)/r,i=(l?V(n.height):n.height)/o;return a&&Number.isFinite(a)||(a=1),i&&Number.isFinite(i)||(i=1),{x:a,y:i}}let eL=Y(0);function ek(e){let t=ey(e);return eP()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:eL}function eD(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let l=e.getBoundingClientRect(),a=eA(e),i=Y(1);t&&(r?eg(r)&&(i=eN(r)):i=eN(e));let u=(void 0===(o=n)&&(o=!1),r&&(!o||r===ey(a))&&o)?ek(a):Y(0),c=(l.left+u.x)/i.x,s=(l.top+u.y)/i.y,f=l.width/i.x,d=l.height/i.y;if(a){let e=ey(a),t=r&&eg(r)?ey(r):r,n=e,o=eS(n);for(;o&&r&&t!==n;){let e=eN(o),t=o.getBoundingClientRect(),r=eO(o),l=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,a=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;c*=e.x,s*=e.y,f*=e.x,d*=e.y,c+=l,s+=a,o=eS(n=ey(o))}}return ea({width:f,height:d,x:c,y:s})}function eI(e,t){let n=eT(e).scrollLeft;return t?t.left+n:eD(ev(e)).left+n}function eU(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:eI(e,r)),y:r.top+t.scrollTop}}function eF(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=ey(e),r=ev(e),o=n.visualViewport,l=r.clientWidth,a=r.clientHeight,i=0,u=0;if(o){l=o.width,a=o.height;let e=eP();(!e||e&&"fixed"===t)&&(i=o.offsetLeft,u=o.offsetTop)}return{width:l,height:a,x:i,y:u}}(e,n);else if("document"===t)r=function(e){let t=ev(e),n=eT(e),r=e.ownerDocument.body,o=G(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),l=G(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),a=-n.scrollLeft+eI(e),i=-n.scrollTop;return"rtl"===eO(r).direction&&(a+=G(t.clientWidth,r.clientWidth)-o),{width:o,height:l,x:a,y:i}}(ev(e));else if(eg(t))r=function(e,t){let n=eD(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,l=eb(e)?eN(e):Y(1),a=e.clientWidth*l.x,i=e.clientHeight*l.y;return{width:a,height:i,x:o*l.x,y:r*l.y}}(t,n);else{let n=ek(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return ea(r)}function eH(e){return"static"===eO(e).position}function eK(e,t){if(!eb(e)||"fixed"===eO(e).position)return null;if(t)return t(e);let n=e.offsetParent;return ev(e)===n&&(n=n.ownerDocument.body),n}function eB(e,t){let n=ey(e);if(e_(e))return n;if(!eb(e)){let t=ej(e);for(;t&&!ex(t);){if(eg(t)&&!eH(t))return t;t=ej(t)}return n}let r=eK(e,t);for(;r&&["table","td","th"].includes(eh(r))&&eH(r);)r=eK(r,t);return r&&ex(r)&&eH(r)&&!eE(r)?n:r||function(e){let t=ej(e);for(;eb(t)&&!ex(t);){if(eE(t))return t;if(e_(t))break;t=ej(t)}return null}(e)||n}let ez=async function(e){let t=this.getOffsetParent||eB,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=eb(t),o=ev(t),l="fixed"===n,a=eD(e,!0,l,t),i={scrollLeft:0,scrollTop:0},u=Y(0);if(r||!r&&!l)if(("body"!==eh(t)||eR(o))&&(i=eT(t)),r){let e=eD(t,!0,l,t);u.x=e.x+t.clientLeft,u.y=e.y+t.clientTop}else o&&(u.x=eI(o));l&&!r&&o&&(u.x=eI(o));let c=!o||r||l?Y(0):eU(o,i);return{x:a.left+i.scrollLeft-u.x-c.x,y:a.top+i.scrollTop-u.y-c.y,width:a.width,height:a.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},eW={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e,l="fixed"===o,a=ev(r),i=!!t&&e_(t.floating);if(r===a||i&&l)return n;let u={scrollLeft:0,scrollTop:0},c=Y(1),s=Y(0),f=eb(r);if((f||!f&&!l)&&(("body"!==eh(r)||eR(a))&&(u=eT(r)),eb(r))){let e=eD(r);c=eN(r),s.x=e.x+r.clientLeft,s.y=e.y+r.clientTop}let d=!a||f||l?Y(0):eU(a,u,!0);return{width:n.width*c.x,height:n.height*c.y,x:n.x*c.x-u.scrollLeft*c.x+s.x+d.x,y:n.y*c.y-u.scrollTop*c.y+s.y+d.y}},getDocumentElement:ev,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e,l=[..."clippingAncestors"===n?e_(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=eM(e,[],!1).filter(e=>eg(e)&&"body"!==eh(e)),o=null,l="fixed"===eO(e).position,a=l?ej(e):e;for(;eg(a)&&!ex(a);){let t=eO(a),n=eE(a);n||"fixed"!==t.position||(o=null),(l?!n&&!o:!n&&"static"===t.position&&!!o&&["absolute","fixed"].includes(o.position)||eR(a)&&!n&&function e(t,n){let r=ej(t);return!(r===n||!eg(r)||ex(r))&&("fixed"===eO(r).position||e(r,n))}(e,a))?r=r.filter(e=>e!==a):o=t,a=ej(a)}return t.set(e,r),r}(t,this._c):[].concat(n),r],a=l[0],i=l.reduce((e,n)=>{let r=eF(t,n,o);return e.top=G(r.top,e.top),e.right=W(r.right,e.right),e.bottom=W(r.bottom,e.bottom),e.left=G(r.left,e.left),e},eF(t,a,o));return{width:i.right-i.left,height:i.bottom-i.top,x:i.left,y:i.top}},getOffsetParent:eB,getElementRects:ez,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=eC(e);return{width:t,height:n}},getScale:eN,isElement:eg,isRTL:function(e){return"rtl"===eO(e).direction}};function eG(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let eV=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:o,rects:l,platform:a,elements:i,middlewareData:u}=t,{element:c,padding:s=0}=Z(e,t)||{};if(null==c)return{};let f=el(s),d={x:n,y:r},p=ee(en(o)),h=et(p),y=await a.getDimensions(c),v="y"===p,m=v?"clientHeight":"clientWidth",g=l.reference[h]+l.reference[p]-d[p]-l.floating[h],b=d[p]-l.reference[p],w=await (null==a.getOffsetParent?void 0:a.getOffsetParent(c)),R=w?w[m]:0;R&&await (null==a.isElement?void 0:a.isElement(w))||(R=i.floating[m]||l.floating[h]);let _=R/2-y[h]/2-1,E=W(f[v?"top":"left"],_),P=W(f[v?"bottom":"right"],_),x=R-y[h]-P,O=R/2-y[h]/2+(g/2-b/2),T=G(E,W(O,x)),j=!u.arrow&&null!=J(o)&&O!==T&&l.reference[h]/2-(O<E?E:P)-y[h]/2<0,M=j?O<E?O-E:O-x:0;return{[p]:d[p]+M,data:{[p]:T,centerOffset:O-T-M,...j&&{alignmentOffset:M}},reset:j}}}),eX=(e,t,n)=>{let r=new Map,o={platform:eW,...n},l={...o.platform,_c:r};return eu(e,t,{...o,platform:l})};var eY=n(51215),eq="undefined"!=typeof document?a.useLayoutEffect:function(){};function e$(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!e$(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){let n=o[r];if(("_owner"!==n||!e.$$typeof)&&!e$(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function eZ(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function eQ(e,t){let n=eZ(e);return Math.round(t*n)/n}function eJ(e){let t=a.useRef(e);return eq(()=>{t.current=e}),t}let e0=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?eV({element:n.current,padding:r}).fn(t):{}:n?eV({element:n,padding:r}).fn(t):{}}}),e1=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:o,y:l,placement:a,middlewareData:i}=t,u=await ed(t,e);return a===(null==(n=i.offset)?void 0:n.placement)&&null!=(r=i.arrow)&&r.alignmentOffset?{}:{x:o+u.x,y:l+u.y,data:{...u,placement:a}}}}}(e),options:[e,t]}),e2=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:o}=t,{mainAxis:l=!0,crossAxis:a=!1,limiter:i={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...u}=Z(e,t),c={x:n,y:r},s=await ec(t,u),f=en(Q(o)),d=ee(f),p=c[d],h=c[f];if(l){let e="y"===d?"top":"left",t="y"===d?"bottom":"right",n=p+s[e],r=p-s[t];p=G(n,W(p,r))}if(a){let e="y"===f?"top":"left",t="y"===f?"bottom":"right",n=h+s[e],r=h-s[t];h=G(n,W(h,r))}let y=i.fn({...t,[d]:p,[f]:h});return{...y,data:{x:y.x-n,y:y.y-r,enabled:{[d]:l,[f]:a}}}}}}(e),options:[e,t]}),e6=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:o,rects:l,middlewareData:a}=t,{offset:i=0,mainAxis:u=!0,crossAxis:c=!0}=Z(e,t),s={x:n,y:r},f=en(o),d=ee(f),p=s[d],h=s[f],y=Z(i,t),v="number"==typeof y?{mainAxis:y,crossAxis:0}:{mainAxis:0,crossAxis:0,...y};if(u){let e="y"===d?"height":"width",t=l.reference[d]-l.floating[e]+v.mainAxis,n=l.reference[d]+l.reference[e]-v.mainAxis;p<t?p=t:p>n&&(p=n)}if(c){var m,g;let e="y"===d?"width":"height",t=["top","left"].includes(Q(o)),n=l.reference[f]-l.floating[e]+(t&&(null==(m=a.offset)?void 0:m[f])||0)+(t?0:v.crossAxis),r=l.reference[f]+l.reference[e]+(t?0:(null==(g=a.offset)?void 0:g[f])||0)-(t?v.crossAxis:0);h<n?h=n:h>r&&(h=r)}return{[d]:p,[f]:h}}}}(e),options:[e,t]}),e3=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,o,l,a;let{placement:i,middlewareData:u,rects:c,initialPlacement:s,platform:f,elements:d}=t,{mainAxis:p=!0,crossAxis:h=!0,fallbackPlacements:y,fallbackStrategy:v="bestFit",fallbackAxisSideDirection:m="none",flipAlignment:g=!0,...b}=Z(e,t);if(null!=(n=u.arrow)&&n.alignmentOffset)return{};let w=Q(i),R=en(s),_=Q(s)===s,E=await (null==f.isRTL?void 0:f.isRTL(d.floating)),P=y||(_||!g?[eo(s)]:function(e){let t=eo(e);return[er(e),t,er(t)]}(s)),x="none"!==m;!y&&x&&P.push(...function(e,t,n,r){let o=J(e),l=function(e,t,n){let r=["left","right"],o=["right","left"];switch(e){case"top":case"bottom":if(n)return t?o:r;return t?r:o;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(Q(e),"start"===n,r);return o&&(l=l.map(e=>e+"-"+o),t&&(l=l.concat(l.map(er)))),l}(s,g,m,E));let O=[s,...P],T=await ec(t,b),j=[],M=(null==(r=u.flip)?void 0:r.overflows)||[];if(p&&j.push(T[w]),h){let e=function(e,t,n){void 0===n&&(n=!1);let r=J(e),o=ee(en(e)),l=et(o),a="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[l]>t.floating[l]&&(a=eo(a)),[a,eo(a)]}(i,c,E);j.push(T[e[0]],T[e[1]])}if(M=[...M,{placement:i,overflows:j}],!j.every(e=>e<=0)){let e=((null==(o=u.flip)?void 0:o.index)||0)+1,t=O[e];if(t&&("alignment"!==h||R===en(t)||M.every(e=>e.overflows[0]>0&&en(e.placement)===R)))return{data:{index:e,overflows:M},reset:{placement:t}};let n=null==(l=M.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:l.placement;if(!n)switch(v){case"bestFit":{let e=null==(a=M.filter(e=>{if(x){let t=en(e.placement);return t===R||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:a[0];e&&(n=e);break}case"initialPlacement":n=s}if(i!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}),e4=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let o,l,{placement:a,rects:i,platform:u,elements:c}=t,{apply:s=()=>{},...f}=Z(e,t),d=await ec(t,f),p=Q(a),h=J(a),y="y"===en(a),{width:v,height:m}=i.floating;"top"===p||"bottom"===p?(o=p,l=h===(await (null==u.isRTL?void 0:u.isRTL(c.floating))?"start":"end")?"left":"right"):(l=p,o="end"===h?"top":"bottom");let g=m-d.top-d.bottom,b=v-d.left-d.right,w=W(m-d[o],g),R=W(v-d[l],b),_=!t.middlewareData.shift,E=w,P=R;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(P=b),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(E=g),_&&!h){let e=G(d.left,0),t=G(d.right,0),n=G(d.top,0),r=G(d.bottom,0);y?P=v-2*(0!==e||0!==t?e+t:G(d.left,d.right)):E=m-2*(0!==n||0!==r?n+r:G(d.top,d.bottom))}await s({...t,availableWidth:P,availableHeight:E});let x=await u.getDimensions(c.floating);return v!==x.width||m!==x.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),e9=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...o}=Z(e,t);switch(r){case"referenceHidden":{let e=es(await ec(t,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:ef(e)}}}case"escaped":{let e=es(await ec(t,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:ef(e)}}}default:return{}}}}}(e),options:[e,t]}),e5=(e,t)=>({...e0(e),options:[e,t]});var e7=a.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...l}=e;return(0,v.jsx)(h.sG.svg,{...l,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,v.jsx)("polygon",{points:"0,0 30,0 15,10"})})});e7.displayName="Arrow";var e8="Popper",[te,tt]=(0,s.A)(e8),[tn,tr]=te(e8),to=e=>{let{__scopePopper:t,children:n}=e,[r,o]=a.useState(null);return(0,v.jsx)(tn,{scope:t,anchor:r,onAnchorChange:o,children:n})};to.displayName=e8;var tl="PopperAnchor",ta=a.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:r,...o}=e,l=tr(tl,n),i=a.useRef(null),u=(0,c.s)(t,i);return a.useEffect(()=>{l.onAnchorChange(r?.current||i.current)}),r?null:(0,v.jsx)(h.sG.div,{...o,ref:u})});ta.displayName=tl;var ti="PopperContent",[tu,tc]=te(ti),ts=a.forwardRef((e,t)=>{let{__scopePopper:n,side:r="bottom",sideOffset:o=0,align:l="center",alignOffset:i=0,arrowPadding:u=0,avoidCollisions:s=!0,collisionBoundary:d=[],collisionPadding:p=0,sticky:y="partial",hideWhenDetached:m=!1,updatePositionStrategy:g="optimized",onPlaced:b,...w}=e,R=tr(ti,n),[_,P]=a.useState(null),x=(0,c.s)(t,e=>P(e)),[O,T]=a.useState(null),j=function(e){let[t,n]=a.useState(void 0);return(0,f.N)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let l=t[0];if("borderBoxSize"in l){let e=l.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}(O),M=j?.width??0,S=j?.height??0,C="number"==typeof p?p:{top:0,right:0,bottom:0,left:0,...p},A=Array.isArray(d)?d:[d],N=A.length>0,L={padding:C,boundary:A.filter(th),altBoundary:N},{refs:k,floatingStyles:D,placement:I,isPositioned:U,middlewareData:F}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:r=[],platform:o,elements:{reference:l,floating:i}={},transform:u=!0,whileElementsMounted:c,open:s}=e,[f,d]=a.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[p,h]=a.useState(r);e$(p,r)||h(r);let[y,v]=a.useState(null),[m,g]=a.useState(null),b=a.useCallback(e=>{e!==E.current&&(E.current=e,v(e))},[]),w=a.useCallback(e=>{e!==P.current&&(P.current=e,g(e))},[]),R=l||y,_=i||m,E=a.useRef(null),P=a.useRef(null),x=a.useRef(f),O=null!=c,T=eJ(c),j=eJ(o),M=eJ(s),S=a.useCallback(()=>{if(!E.current||!P.current)return;let e={placement:t,strategy:n,middleware:p};j.current&&(e.platform=j.current),eX(E.current,P.current,e).then(e=>{let t={...e,isPositioned:!1!==M.current};C.current&&!e$(x.current,t)&&(x.current=t,eY.flushSync(()=>{d(t)}))})},[p,t,n,j,M]);eq(()=>{!1===s&&x.current.isPositioned&&(x.current.isPositioned=!1,d(e=>({...e,isPositioned:!1})))},[s]);let C=a.useRef(!1);eq(()=>(C.current=!0,()=>{C.current=!1}),[]),eq(()=>{if(R&&(E.current=R),_&&(P.current=_),R&&_){if(T.current)return T.current(R,_,S);S()}},[R,_,S,T,O]);let A=a.useMemo(()=>({reference:E,floating:P,setReference:b,setFloating:w}),[b,w]),N=a.useMemo(()=>({reference:R,floating:_}),[R,_]),L=a.useMemo(()=>{let e={position:n,left:0,top:0};if(!N.floating)return e;let t=eQ(N.floating,f.x),r=eQ(N.floating,f.y);return u?{...e,transform:"translate("+t+"px, "+r+"px)",...eZ(N.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,u,N.floating,f.x,f.y]);return a.useMemo(()=>({...f,update:S,refs:A,elements:N,floatingStyles:L}),[f,S,A,N,L])}({strategy:"fixed",placement:r+("center"!==l?"-"+l:""),whileElementsMounted:(...e)=>(function(e,t,n,r){let o;void 0===r&&(r={});let{ancestorScroll:l=!0,ancestorResize:a=!0,elementResize:i="function"==typeof ResizeObserver,layoutShift:u="function"==typeof IntersectionObserver,animationFrame:c=!1}=r,s=eA(e),f=l||a?[...s?eM(s):[],...eM(t)]:[];f.forEach(e=>{l&&e.addEventListener("scroll",n,{passive:!0}),a&&e.addEventListener("resize",n)});let d=s&&u?function(e,t){let n,r=null,o=ev(e);function l(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function a(i,u){void 0===i&&(i=!1),void 0===u&&(u=1),l();let c=e.getBoundingClientRect(),{left:s,top:f,width:d,height:p}=c;if(i||t(),!d||!p)return;let h=X(f),y=X(o.clientWidth-(s+d)),v={rootMargin:-h+"px "+-y+"px "+-X(o.clientHeight-(f+p))+"px "+-X(s)+"px",threshold:G(0,W(1,u))||1},m=!0;function g(t){let r=t[0].intersectionRatio;if(r!==u){if(!m)return a();r?a(!1,r):n=setTimeout(()=>{a(!1,1e-7)},1e3)}1!==r||eG(c,e.getBoundingClientRect())||a(),m=!1}try{r=new IntersectionObserver(g,{...v,root:o.ownerDocument})}catch(e){r=new IntersectionObserver(g,v)}r.observe(e)}(!0),l}(s,n):null,p=-1,h=null;i&&(h=new ResizeObserver(e=>{let[r]=e;r&&r.target===s&&h&&(h.unobserve(t),cancelAnimationFrame(p),p=requestAnimationFrame(()=>{var e;null==(e=h)||e.observe(t)})),n()}),s&&!c&&h.observe(s),h.observe(t));let y=c?eD(e):null;return c&&function t(){let r=eD(e);y&&!eG(y,r)&&n(),y=r,o=requestAnimationFrame(t)}(),n(),()=>{var e;f.forEach(e=>{l&&e.removeEventListener("scroll",n),a&&e.removeEventListener("resize",n)}),null==d||d(),null==(e=h)||e.disconnect(),h=null,c&&cancelAnimationFrame(o)}})(...e,{animationFrame:"always"===g}),elements:{reference:R.anchor},middleware:[e1({mainAxis:o+S,alignmentAxis:i}),s&&e2({mainAxis:!0,crossAxis:!1,limiter:"partial"===y?e6():void 0,...L}),s&&e3({...L}),e4({...L,apply:({elements:e,rects:t,availableWidth:n,availableHeight:r})=>{let{width:o,height:l}=t.reference,a=e.floating.style;a.setProperty("--radix-popper-available-width",`${n}px`),a.setProperty("--radix-popper-available-height",`${r}px`),a.setProperty("--radix-popper-anchor-width",`${o}px`),a.setProperty("--radix-popper-anchor-height",`${l}px`)}}),O&&e5({element:O,padding:u}),ty({arrowWidth:M,arrowHeight:S}),m&&e9({strategy:"referenceHidden",...L})]}),[H,K]=tv(I),B=(0,E.c)(b);(0,f.N)(()=>{U&&B?.()},[U,B]);let z=F.arrow?.x,V=F.arrow?.y,Y=F.arrow?.centerOffset!==0,[q,$]=a.useState();return(0,f.N)(()=>{_&&$(window.getComputedStyle(_).zIndex)},[_]),(0,v.jsx)("div",{ref:k.setFloating,"data-radix-popper-content-wrapper":"",style:{...D,transform:U?D.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:q,"--radix-popper-transform-origin":[F.transformOrigin?.x,F.transformOrigin?.y].join(" "),...F.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,v.jsx)(tu,{scope:n,placedSide:H,onArrowChange:T,arrowX:z,arrowY:V,shouldHideArrow:Y,children:(0,v.jsx)(h.sG.div,{"data-side":H,"data-align":K,...w,ref:x,style:{...w.style,animation:U?void 0:"none"}})})})});ts.displayName=ti;var tf="PopperArrow",td={top:"bottom",right:"left",bottom:"top",left:"right"},tp=a.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=tc(tf,n),l=td[o.placedSide];return(0,v.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[l]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,v.jsx)(e7,{...r,ref:t,style:{...r.style,display:"block"}})})});function th(e){return null!==e}tp.displayName=tf;var ty=e=>({name:"transformOrigin",options:e,fn(t){let{placement:n,rects:r,middlewareData:o}=t,l=o.arrow?.centerOffset!==0,a=l?0:e.arrowWidth,i=l?0:e.arrowHeight,[u,c]=tv(n),s={start:"0%",center:"50%",end:"100%"}[c],f=(o.arrow?.x??0)+a/2,d=(o.arrow?.y??0)+i/2,p="",h="";return"bottom"===u?(p=l?s:`${f}px`,h=`${-i}px`):"top"===u?(p=l?s:`${f}px`,h=`${r.floating.height+i}px`):"right"===u?(p=`${-i}px`,h=l?s:`${d}px`):"left"===u&&(p=`${r.floating.width+i}px`,h=l?s:`${d}px`),{data:{x:p,y:h}}}});function tv(e){let[t,n="center"]=e.split("-");return[t,n]}var tm=a.forwardRef((e,t)=>{let{container:n,...r}=e,[o,l]=a.useState(!1);(0,f.N)(()=>l(!0),[]);let i=n||o&&globalThis?.document?.body;return i?eY.createPortal((0,v.jsx)(h.sG.div,{...r,ref:t}),i):null});tm.displayName="Portal";var tg=e=>{let{present:t,children:n}=e,r=function(e){var t,n;let[r,o]=a.useState(),l=a.useRef(null),i=a.useRef(e),u=a.useRef("none"),[c,s]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},a.useReducer((e,t)=>n[e][t]??e,t));return a.useEffect(()=>{let e=tb(l.current);u.current="mounted"===c?e:"none"},[c]),(0,f.N)(()=>{let t=l.current,n=i.current;if(n!==e){let r=u.current,o=tb(t);e?s("MOUNT"):"none"===o||t?.display==="none"?s("UNMOUNT"):n&&r!==o?s("ANIMATION_OUT"):s("UNMOUNT"),i.current=e}},[e,s]),(0,f.N)(()=>{if(r){let e,t=r.ownerDocument.defaultView??window,n=n=>{let o=tb(l.current).includes(n.animationName);if(n.target===r&&o&&(s("ANIMATION_END"),!i.current)){let n=r.style.animationFillMode;r.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===r.style.animationFillMode&&(r.style.animationFillMode=n)})}},o=e=>{e.target===r&&(u.current=tb(l.current))};return r.addEventListener("animationstart",o),r.addEventListener("animationcancel",n),r.addEventListener("animationend",n),()=>{t.clearTimeout(e),r.removeEventListener("animationstart",o),r.removeEventListener("animationcancel",n),r.removeEventListener("animationend",n)}}s("ANIMATION_END")},[r,s]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:a.useCallback(e=>{l.current=e?getComputedStyle(e):null,o(e)},[])}}(t),o="function"==typeof n?n({present:r.isPresent}):a.Children.only(n),l=(0,c.s)(r.ref,function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(n=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(o));return"function"==typeof n||r.isPresent?a.cloneElement(o,{ref:l}):null};function tb(e){return e?.animationName||"none"}tg.displayName="Presence";var tw="rovingFocusGroup.onEntryFocus",tR={bubbles:!1,cancelable:!0},t_="RovingFocusGroup",[tE,tP,tx]=m(t_),[tO,tT]=(0,s.A)(t_,[tx]),[tj,tM]=tO(t_),tS=a.forwardRef((e,t)=>(0,v.jsx)(tE.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,v.jsx)(tE.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,v.jsx)(tC,{...e,ref:t})})}));tS.displayName=t_;var tC=a.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,orientation:r,loop:o=!1,dir:l,currentTabStopId:i,defaultCurrentTabStopId:s,onCurrentTabStopIdChange:f,onEntryFocus:d,preventScrollOnEntryFocus:y=!1,...m}=e,g=a.useRef(null),b=(0,c.s)(t,g),w=_(l),[R,P]=p({prop:i,defaultProp:s??null,onChange:f,caller:t_}),[x,O]=a.useState(!1),T=(0,E.c)(d),j=tP(n),M=a.useRef(!1),[S,C]=a.useState(0);return a.useEffect(()=>{let e=g.current;if(e)return e.addEventListener(tw,T),()=>e.removeEventListener(tw,T)},[T]),(0,v.jsx)(tj,{scope:n,orientation:r,dir:w,loop:o,currentTabStopId:R,onItemFocus:a.useCallback(e=>P(e),[P]),onItemShiftTab:a.useCallback(()=>O(!0),[]),onFocusableItemAdd:a.useCallback(()=>C(e=>e+1),[]),onFocusableItemRemove:a.useCallback(()=>C(e=>e-1),[]),children:(0,v.jsx)(h.sG.div,{tabIndex:x||0===S?-1:0,"data-orientation":r,...m,ref:b,style:{outline:"none",...e.style},onMouseDown:u(e.onMouseDown,()=>{M.current=!0}),onFocus:u(e.onFocus,e=>{let t=!M.current;if(e.target===e.currentTarget&&t&&!x){let t=new CustomEvent(tw,tR);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=j().filter(e=>e.focusable);tk([e.find(e=>e.active),e.find(e=>e.id===R),...e].filter(Boolean).map(e=>e.ref.current),y)}}M.current=!1}),onBlur:u(e.onBlur,()=>O(!1))})})}),tA="RovingFocusGroupItem",tN=a.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,focusable:r=!0,active:o=!1,tabStopId:l,children:i,...c}=e,s=B(),f=l||s,d=tM(tA,n),p=d.currentTabStopId===f,y=tP(n),{onFocusableItemAdd:m,onFocusableItemRemove:g,currentTabStopId:b}=d;return a.useEffect(()=>{if(r)return m(),()=>g()},[r,m,g]),(0,v.jsx)(tE.ItemSlot,{scope:n,id:f,focusable:r,active:o,children:(0,v.jsx)(h.sG.span,{tabIndex:p?0:-1,"data-orientation":d.orientation,...c,ref:t,onMouseDown:u(e.onMouseDown,e=>{r?d.onItemFocus(f):e.preventDefault()}),onFocus:u(e.onFocus,()=>d.onItemFocus(f)),onKeyDown:u(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void d.onItemShiftTab();if(e.target!==e.currentTarget)return;let t=function(e,t,n){var r;let o=(r=e.key,"rtl"!==n?r:"ArrowLeft"===r?"ArrowRight":"ArrowRight"===r?"ArrowLeft":r);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return tL[o]}(e,d.orientation,d.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let n=y().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)n.reverse();else if("prev"===t||"next"===t){"prev"===t&&n.reverse();let r=n.indexOf(e.currentTarget);n=d.loop?function(e,t){return e.map((n,r)=>e[(t+r)%e.length])}(n,r+1):n.slice(r+1)}setTimeout(()=>tk(n))}}),children:"function"==typeof i?i({isCurrentTabStop:p,hasTabStop:null!=b}):i})})});tN.displayName=tA;var tL={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function tk(e,t=!1){let n=document.activeElement;for(let r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}var tD=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},tI=new WeakMap,tU=new WeakMap,tF={},tH=0,tK=function(e){return e&&(e.host||tK(e.parentNode))},tB=function(e,t,n,r){var o=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=tK(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});tF[n]||(tF[n]=new WeakMap);var l=tF[n],a=[],i=new Set,u=new Set(o),c=function(e){!e||i.has(e)||(i.add(e),c(e.parentNode))};o.forEach(c);var s=function(e){!e||u.has(e)||Array.prototype.forEach.call(e.children,function(e){if(i.has(e))s(e);else try{var t=e.getAttribute(r),o=null!==t&&"false"!==t,u=(tI.get(e)||0)+1,c=(l.get(e)||0)+1;tI.set(e,u),l.set(e,c),a.push(e),1===u&&o&&tU.set(e,!0),1===c&&e.setAttribute(n,"true"),o||e.setAttribute(r,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return s(t),i.clear(),tH++,function(){a.forEach(function(e){var t=tI.get(e)-1,o=l.get(e)-1;tI.set(e,t),l.set(e,o),t||(tU.has(e)||e.removeAttribute(r),tU.delete(e)),o||e.removeAttribute(n)}),--tH||(tI=new WeakMap,tI=new WeakMap,tU=new WeakMap,tF={})}},tz=function(e,t,n){void 0===n&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=t||tD(e);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live], script"))),tB(r,o,n,"aria-hidden")):function(){return null}},tW=function(){return(tW=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function tG(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}Object.create;Object.create;var tV=("function"==typeof SuppressedError&&SuppressedError,"right-scroll-bar-position"),tX="width-before-scroll-bar";function tY(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var tq="undefined"!=typeof window?a.useLayoutEffect:a.useEffect,t$=new WeakMap;function tZ(e){return e}var tQ=function(e){void 0===e&&(e={});var t,n,r,o,l=(t=null,void 0===n&&(n=tZ),r=[],o=!1,{read:function(){if(o)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return r.length?r[r.length-1]:null},useMedium:function(e){var t=n(e,o);return r.push(t),function(){r=r.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(o=!0;r.length;){var t=r;r=[],t.forEach(e)}r={push:function(t){return e(t)},filter:function(){return r}}},assignMedium:function(e){o=!0;var t=[];if(r.length){var n=r;r=[],n.forEach(e),t=r}var l=function(){var n=t;t=[],n.forEach(e)},a=function(){return Promise.resolve().then(l)};a(),r={push:function(e){t.push(e),a()},filter:function(e){return t=t.filter(e),r}}}});return l.options=tW({async:!0,ssr:!1},e),l}(),tJ=function(){},t0=a.forwardRef(function(e,t){var n,r,o,l,i=a.useRef(null),u=a.useState({onScrollCapture:tJ,onWheelCapture:tJ,onTouchMoveCapture:tJ}),c=u[0],s=u[1],f=e.forwardProps,d=e.children,p=e.className,h=e.removeScrollBar,y=e.enabled,v=e.shards,m=e.sideCar,g=e.noRelative,b=e.noIsolation,w=e.inert,R=e.allowPinchZoom,_=e.as,E=e.gapMode,P=tG(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),x=(n=[i,t],r=function(e){return n.forEach(function(t){return tY(t,e)})},(o=(0,a.useState)(function(){return{value:null,callback:r,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=r,l=o.facade,tq(function(){var e=t$.get(l);if(e){var t=new Set(e),r=new Set(n),o=l.current;t.forEach(function(e){r.has(e)||tY(e,null)}),r.forEach(function(e){t.has(e)||tY(e,o)})}t$.set(l,n)},[n]),l),O=tW(tW({},P),c);return a.createElement(a.Fragment,null,y&&a.createElement(m,{sideCar:tQ,removeScrollBar:h,shards:v,noRelative:g,noIsolation:b,inert:w,setCallbacks:s,allowPinchZoom:!!R,lockRef:i,gapMode:E}),f?a.cloneElement(a.Children.only(d),tW(tW({},O),{ref:x})):a.createElement(void 0===_?"div":_,tW({},O,{className:p,ref:x}),d))});t0.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},t0.classNames={fullWidth:tX,zeroRight:tV};var t1=function(e){var t=e.sideCar,n=tG(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return a.createElement(r,tW({},n))};t1.isSideCarExport=!0;var t2=function(){var e=0,t=null;return{add:function(r){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=l||n.nc;return t&&e.setAttribute("nonce",t),e}())){var o,a;(o=t).styleSheet?o.styleSheet.cssText=r:o.appendChild(document.createTextNode(r)),a=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(a)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},t6=function(){var e=t2();return function(t,n){a.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},t3=function(){var e=t6();return function(t){return e(t.styles,t.dynamic),null}},t4={left:0,top:0,right:0,gap:0},t9=function(e){return parseInt(e||"",10)||0},t5=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[t9(n),t9(r),t9(o)]},t7=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return t4;var t=t5(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},t8=t3(),ne="data-scroll-locked",nt=function(e,t,n,r){var o=e.left,l=e.top,a=e.right,i=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(i,"px ").concat(r,";\n  }\n  body[").concat(ne,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(l,"px;\n    padding-right: ").concat(a,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(i,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(i,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(tV," {\n    right: ").concat(i,"px ").concat(r,";\n  }\n  \n  .").concat(tX," {\n    margin-right: ").concat(i,"px ").concat(r,";\n  }\n  \n  .").concat(tV," .").concat(tV," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(tX," .").concat(tX," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(ne,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(i,"px;\n  }\n")},nn=function(){var e=parseInt(document.body.getAttribute(ne)||"0",10);return isFinite(e)?e:0},nr=function(){a.useEffect(function(){return document.body.setAttribute(ne,(nn()+1).toString()),function(){var e=nn()-1;e<=0?document.body.removeAttribute(ne):document.body.setAttribute(ne,e.toString())}},[])},no=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;nr();var l=a.useMemo(function(){return t7(o)},[o]);return a.createElement(t8,{styles:nt(l,!t,o,n?"":"!important")})},nl=!1;if("undefined"!=typeof window)try{var na=Object.defineProperty({},"passive",{get:function(){return nl=!0,!0}});window.addEventListener("test",na,na),window.removeEventListener("test",na,na)}catch(e){nl=!1}var ni=!!nl&&{passive:!1},nu=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},nc=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),ns(e,r)){var o=nf(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},ns=function(e,t){return"v"===e?nu(t,"overflowY"):nu(t,"overflowX")},nf=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},nd=function(e,t,n,r,o){var l,a=(l=window.getComputedStyle(t).direction,"h"===e&&"rtl"===l?-1:1),i=a*r,u=n.target,c=t.contains(u),s=!1,f=i>0,d=0,p=0;do{if(!u)break;var h=nf(e,u),y=h[0],v=h[1]-h[2]-a*y;(y||v)&&ns(e,u)&&(d+=v,p+=y);var m=u.parentNode;u=m&&m.nodeType===Node.DOCUMENT_FRAGMENT_NODE?m.host:m}while(!c&&u!==document.body||c&&(t.contains(u)||t===u));return f&&(o&&1>Math.abs(d)||!o&&i>d)?s=!0:!f&&(o&&1>Math.abs(p)||!o&&-i>p)&&(s=!0),s},np=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},nh=function(e){return[e.deltaX,e.deltaY]},ny=function(e){return e&&"current"in e?e.current:e},nv=0,nm=[];let ng=(r=function(e){var t=a.useRef([]),n=a.useRef([0,0]),r=a.useRef(),o=a.useState(nv++)[0],l=a.useState(t3)[0],i=a.useRef(e);a.useEffect(function(){i.current=e},[e]),a.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,o=0,l=t.length;o<l;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(ny),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var u=a.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!i.current.allowPinchZoom;var o,l=np(e),a=n.current,u="deltaX"in e?e.deltaX:a[0]-l[0],c="deltaY"in e?e.deltaY:a[1]-l[1],s=e.target,f=Math.abs(u)>Math.abs(c)?"h":"v";if("touches"in e&&"h"===f&&"range"===s.type)return!1;var d=nc(f,s);if(!d)return!0;if(d?o=f:(o="v"===f?"h":"v",d=nc(f,s)),!d)return!1;if(!r.current&&"changedTouches"in e&&(u||c)&&(r.current=o),!o)return!0;var p=r.current||o;return nd(p,t,e,"h"===p?u:c,!0)},[]),c=a.useCallback(function(e){if(nm.length&&nm[nm.length-1]===l){var n="deltaY"in e?nh(e):np(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta,r[0]===n[0]&&r[1]===n[1])})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(i.current.shards||[]).map(ny).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?u(e,o[0]):!i.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),s=a.useCallback(function(e,n,r,o){var l={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(l),setTimeout(function(){t.current=t.current.filter(function(e){return e!==l})},1)},[]),f=a.useCallback(function(e){n.current=np(e),r.current=void 0},[]),d=a.useCallback(function(t){s(t.type,nh(t),t.target,u(t,e.lockRef.current))},[]),p=a.useCallback(function(t){s(t.type,np(t),t.target,u(t,e.lockRef.current))},[]);a.useEffect(function(){return nm.push(l),e.setCallbacks({onScrollCapture:d,onWheelCapture:d,onTouchMoveCapture:p}),document.addEventListener("wheel",c,ni),document.addEventListener("touchmove",c,ni),document.addEventListener("touchstart",f,ni),function(){nm=nm.filter(function(e){return e!==l}),document.removeEventListener("wheel",c,ni),document.removeEventListener("touchmove",c,ni),document.removeEventListener("touchstart",f,ni)}},[]);var h=e.removeScrollBar,y=e.inert;return a.createElement(a.Fragment,null,y?a.createElement(l,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,h?a.createElement(no,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},tQ.useMedium(r),t1);var nb=a.forwardRef(function(e,t){return a.createElement(t0,tW({},e,{ref:t,sideCar:ng}))});nb.classNames=t0.classNames;var nw=["Enter"," "],nR=["ArrowUp","PageDown","End"],n_=["ArrowDown","PageUp","Home",...nR],nE={ltr:[...nw,"ArrowRight"],rtl:[...nw,"ArrowLeft"]},nP={ltr:["ArrowLeft"],rtl:["ArrowRight"]},nx="Menu",[nO,nT,nj]=m(nx),[nM,nS]=(0,s.A)(nx,[nj,tt,tT]),nC=tt(),nA=tT(),[nN,nL]=nM(nx),[nk,nD]=nM(nx),nI=e=>{let{__scopeMenu:t,open:n=!1,children:r,dir:o,onOpenChange:l,modal:i=!0}=e,u=nC(t),[c,s]=a.useState(null),f=a.useRef(!1),d=(0,E.c)(l),p=_(o);return a.useEffect(()=>{let e=()=>{f.current=!0,document.addEventListener("pointerdown",t,{capture:!0,once:!0}),document.addEventListener("pointermove",t,{capture:!0,once:!0})},t=()=>f.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",t,{capture:!0}),document.removeEventListener("pointermove",t,{capture:!0})}},[]),(0,v.jsx)(to,{...u,children:(0,v.jsx)(nN,{scope:t,open:n,onOpenChange:d,content:c,onContentChange:s,children:(0,v.jsx)(nk,{scope:t,onClose:a.useCallback(()=>d(!1),[d]),isUsingKeyboardRef:f,dir:p,modal:i,children:r})})})};nI.displayName=nx;var nU=a.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e,o=nC(n);return(0,v.jsx)(ta,{...o,...r,ref:t})});nU.displayName="MenuAnchor";var nF="MenuPortal",[nH,nK]=nM(nF,{forceMount:void 0}),nB=e=>{let{__scopeMenu:t,forceMount:n,children:r,container:o}=e,l=nL(nF,t);return(0,v.jsx)(nH,{scope:t,forceMount:n,children:(0,v.jsx)(tg,{present:n||l.open,children:(0,v.jsx)(tm,{asChild:!0,container:o,children:r})})})};nB.displayName=nF;var nz="MenuContent",[nW,nG]=nM(nz),nV=a.forwardRef((e,t)=>{let n=nK(nz,e.__scopeMenu),{forceMount:r=n.forceMount,...o}=e,l=nL(nz,e.__scopeMenu),a=nD(nz,e.__scopeMenu);return(0,v.jsx)(nO.Provider,{scope:e.__scopeMenu,children:(0,v.jsx)(tg,{present:r||l.open,children:(0,v.jsx)(nO.Slot,{scope:e.__scopeMenu,children:a.modal?(0,v.jsx)(nX,{...o,ref:t}):(0,v.jsx)(nY,{...o,ref:t})})})})}),nX=a.forwardRef((e,t)=>{let n=nL(nz,e.__scopeMenu),r=a.useRef(null),o=(0,c.s)(t,r);return a.useEffect(()=>{let e=r.current;if(e)return tz(e)},[]),(0,v.jsx)(n$,{...e,ref:o,trapFocus:n.open,disableOutsidePointerEvents:n.open,disableOutsideScroll:!0,onFocusOutside:u(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>n.onOpenChange(!1)})}),nY=a.forwardRef((e,t)=>{let n=nL(nz,e.__scopeMenu);return(0,v.jsx)(n$,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>n.onOpenChange(!1)})}),nq=(0,y.TL)("MenuContent.ScrollLock"),n$=a.forwardRef((e,t)=>{let{__scopeMenu:n,loop:r=!1,trapFocus:o,onOpenAutoFocus:l,onCloseAutoFocus:i,disableOutsidePointerEvents:s,onEntryFocus:f,onEscapeKeyDown:d,onPointerDownOutside:p,onFocusOutside:h,onInteractOutside:y,onDismiss:m,disableOutsideScroll:g,...b}=e,w=nL(nz,n),R=nD(nz,n),_=nC(n),E=nA(n),P=nT(n),[x,T]=a.useState(null),j=a.useRef(null),C=(0,c.s)(t,j,w.onContentChange),A=a.useRef(0),N=a.useRef(""),k=a.useRef(0),D=a.useRef(null),I=a.useRef("right"),U=a.useRef(0),F=g?nb:a.Fragment,H=e=>{let t=N.current+e,n=P().filter(e=>!e.disabled),r=document.activeElement,o=n.find(e=>e.ref.current===r)?.textValue,l=function(e,t,n){var r;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,l=n?e.indexOf(n):-1,a=(r=Math.max(l,0),e.map((t,n)=>e[(r+n)%e.length]));1===o.length&&(a=a.filter(e=>e!==n));let i=a.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return i!==n?i:void 0}(n.map(e=>e.textValue),t,o),a=n.find(e=>e.textValue===l)?.ref.current;!function e(t){N.current=t,window.clearTimeout(A.current),""!==t&&(A.current=window.setTimeout(()=>e(""),1e3))}(t),a&&setTimeout(()=>a.focus())};a.useEffect(()=>()=>window.clearTimeout(A.current),[]),a.useEffect(()=>{let e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??S()),document.body.insertAdjacentElement("beforeend",e[1]??S()),M++,()=>{1===M&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),M--}},[]);let K=a.useCallback(e=>I.current===D.current?.side&&function(e,t){return!!t&&function(e,t){let{x:n,y:r}=e,o=!1;for(let e=0,l=t.length-1;e<t.length;l=e++){let a=t[e],i=t[l],u=a.x,c=a.y,s=i.x,f=i.y;c>r!=f>r&&n<(s-u)*(r-c)/(f-c)+u&&(o=!o)}return o}({x:e.clientX,y:e.clientY},t)}(e,D.current?.area),[]);return(0,v.jsx)(nW,{scope:n,searchRef:N,onItemEnter:a.useCallback(e=>{K(e)&&e.preventDefault()},[K]),onItemLeave:a.useCallback(e=>{K(e)||(j.current?.focus(),T(null))},[K]),onTriggerLeave:a.useCallback(e=>{K(e)&&e.preventDefault()},[K]),pointerGraceTimerRef:k,onPointerGraceIntentChange:a.useCallback(e=>{D.current=e},[]),children:(0,v.jsx)(F,{...g?{as:nq,allowPinchZoom:!0}:void 0,children:(0,v.jsx)(L,{asChild:!0,trapped:o,onMountAutoFocus:u(l,e=>{e.preventDefault(),j.current?.focus({preventScroll:!0})}),onUnmountAutoFocus:i,children:(0,v.jsx)(O,{asChild:!0,disableOutsidePointerEvents:s,onEscapeKeyDown:d,onPointerDownOutside:p,onFocusOutside:h,onInteractOutside:y,onDismiss:m,children:(0,v.jsx)(tS,{asChild:!0,...E,dir:R.dir,orientation:"vertical",loop:r,currentTabStopId:x,onCurrentTabStopIdChange:T,onEntryFocus:u(f,e=>{R.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,v.jsx)(ts,{role:"menu","aria-orientation":"vertical","data-state":rd(w.open),"data-radix-menu-content":"",dir:R.dir,..._,...b,ref:C,style:{outline:"none",...b.style},onKeyDown:u(b.onKeyDown,e=>{let t=e.target.closest("[data-radix-menu-content]")===e.currentTarget,n=e.ctrlKey||e.altKey||e.metaKey,r=1===e.key.length;t&&("Tab"===e.key&&e.preventDefault(),!n&&r&&H(e.key));let o=j.current;if(e.target!==o||!n_.includes(e.key))return;e.preventDefault();let l=P().filter(e=>!e.disabled).map(e=>e.ref.current);nR.includes(e.key)&&l.reverse(),function(e){let t=document.activeElement;for(let n of e)if(n===t||(n.focus(),document.activeElement!==t))return}(l)}),onBlur:u(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(A.current),N.current="")}),onPointerMove:u(e.onPointerMove,ry(e=>{let t=e.target,n=U.current!==e.clientX;e.currentTarget.contains(t)&&n&&(I.current=e.clientX>U.current?"right":"left",U.current=e.clientX)}))})})})})})})});nV.displayName=nz;var nZ=a.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,v.jsx)(h.sG.div,{role:"group",...r,ref:t})});nZ.displayName="MenuGroup";var nQ=a.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,v.jsx)(h.sG.div,{...r,ref:t})});nQ.displayName="MenuLabel";var nJ="MenuItem",n0="menu.itemSelect",n1=a.forwardRef((e,t)=>{let{disabled:n=!1,onSelect:r,...o}=e,l=a.useRef(null),i=nD(nJ,e.__scopeMenu),s=nG(nJ,e.__scopeMenu),f=(0,c.s)(t,l),d=a.useRef(!1);return(0,v.jsx)(n2,{...o,ref:f,disabled:n,onClick:u(e.onClick,()=>{let e=l.current;if(!n&&e){let t=new CustomEvent(n0,{bubbles:!0,cancelable:!0});e.addEventListener(n0,e=>r?.(e),{once:!0}),(0,h.hO)(e,t),t.defaultPrevented?d.current=!1:i.onClose()}}),onPointerDown:t=>{e.onPointerDown?.(t),d.current=!0},onPointerUp:u(e.onPointerUp,e=>{d.current||e.currentTarget?.click()}),onKeyDown:u(e.onKeyDown,e=>{let t=""!==s.searchRef.current;n||t&&" "===e.key||nw.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});n1.displayName=nJ;var n2=a.forwardRef((e,t)=>{let{__scopeMenu:n,disabled:r=!1,textValue:o,...l}=e,i=nG(nJ,n),s=nA(n),f=a.useRef(null),d=(0,c.s)(t,f),[p,y]=a.useState(!1),[m,g]=a.useState("");return a.useEffect(()=>{let e=f.current;e&&g((e.textContent??"").trim())},[l.children]),(0,v.jsx)(nO.ItemSlot,{scope:n,disabled:r,textValue:o??m,children:(0,v.jsx)(tN,{asChild:!0,...s,focusable:!r,children:(0,v.jsx)(h.sG.div,{role:"menuitem","data-highlighted":p?"":void 0,"aria-disabled":r||void 0,"data-disabled":r?"":void 0,...l,ref:d,onPointerMove:u(e.onPointerMove,ry(e=>{r?i.onItemLeave(e):(i.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:u(e.onPointerLeave,ry(e=>i.onItemLeave(e))),onFocus:u(e.onFocus,()=>y(!0)),onBlur:u(e.onBlur,()=>y(!1))})})})}),n6=a.forwardRef((e,t)=>{let{checked:n=!1,onCheckedChange:r,...o}=e;return(0,v.jsx)(rt,{scope:e.__scopeMenu,checked:n,children:(0,v.jsx)(n1,{role:"menuitemcheckbox","aria-checked":rp(n)?"mixed":n,...o,ref:t,"data-state":rh(n),onSelect:u(o.onSelect,()=>r?.(!!rp(n)||!n),{checkForDefaultPrevented:!1})})})});n6.displayName="MenuCheckboxItem";var n3="MenuRadioGroup",[n4,n9]=nM(n3,{value:void 0,onValueChange:()=>{}}),n5=a.forwardRef((e,t)=>{let{value:n,onValueChange:r,...o}=e,l=(0,E.c)(r);return(0,v.jsx)(n4,{scope:e.__scopeMenu,value:n,onValueChange:l,children:(0,v.jsx)(nZ,{...o,ref:t})})});n5.displayName=n3;var n7="MenuRadioItem",n8=a.forwardRef((e,t)=>{let{value:n,...r}=e,o=n9(n7,e.__scopeMenu),l=n===o.value;return(0,v.jsx)(rt,{scope:e.__scopeMenu,checked:l,children:(0,v.jsx)(n1,{role:"menuitemradio","aria-checked":l,...r,ref:t,"data-state":rh(l),onSelect:u(r.onSelect,()=>o.onValueChange?.(n),{checkForDefaultPrevented:!1})})})});n8.displayName=n7;var re="MenuItemIndicator",[rt,rn]=nM(re,{checked:!1}),rr=a.forwardRef((e,t)=>{let{__scopeMenu:n,forceMount:r,...o}=e,l=rn(re,n);return(0,v.jsx)(tg,{present:r||rp(l.checked)||!0===l.checked,children:(0,v.jsx)(h.sG.span,{...o,ref:t,"data-state":rh(l.checked)})})});rr.displayName=re;var ro=a.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,v.jsx)(h.sG.div,{role:"separator","aria-orientation":"horizontal",...r,ref:t})});ro.displayName="MenuSeparator";var rl=a.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e,o=nC(n);return(0,v.jsx)(tp,{...o,...r,ref:t})});rl.displayName="MenuArrow";var[ra,ri]=nM("MenuSub"),ru="MenuSubTrigger",rc=a.forwardRef((e,t)=>{let n=nL(ru,e.__scopeMenu),r=nD(ru,e.__scopeMenu),o=ri(ru,e.__scopeMenu),l=nG(ru,e.__scopeMenu),i=a.useRef(null),{pointerGraceTimerRef:s,onPointerGraceIntentChange:f}=l,d={__scopeMenu:e.__scopeMenu},p=a.useCallback(()=>{i.current&&window.clearTimeout(i.current),i.current=null},[]);return a.useEffect(()=>p,[p]),a.useEffect(()=>{let e=s.current;return()=>{window.clearTimeout(e),f(null)}},[s,f]),(0,v.jsx)(nU,{asChild:!0,...d,children:(0,v.jsx)(n2,{id:o.triggerId,"aria-haspopup":"menu","aria-expanded":n.open,"aria-controls":o.contentId,"data-state":rd(n.open),...e,ref:(0,c.t)(t,o.onTriggerChange),onClick:t=>{e.onClick?.(t),e.disabled||t.defaultPrevented||(t.currentTarget.focus(),n.open||n.onOpenChange(!0))},onPointerMove:u(e.onPointerMove,ry(t=>{l.onItemEnter(t),!t.defaultPrevented&&(e.disabled||n.open||i.current||(l.onPointerGraceIntentChange(null),i.current=window.setTimeout(()=>{n.onOpenChange(!0),p()},100)))})),onPointerLeave:u(e.onPointerLeave,ry(e=>{p();let t=n.content?.getBoundingClientRect();if(t){let r=n.content?.dataset.side,o="right"===r,a=t[o?"left":"right"],i=t[o?"right":"left"];l.onPointerGraceIntentChange({area:[{x:e.clientX+(o?-5:5),y:e.clientY},{x:a,y:t.top},{x:i,y:t.top},{x:i,y:t.bottom},{x:a,y:t.bottom}],side:r}),window.clearTimeout(s.current),s.current=window.setTimeout(()=>l.onPointerGraceIntentChange(null),300)}else{if(l.onTriggerLeave(e),e.defaultPrevented)return;l.onPointerGraceIntentChange(null)}})),onKeyDown:u(e.onKeyDown,t=>{let o=""!==l.searchRef.current;e.disabled||o&&" "===t.key||nE[r.dir].includes(t.key)&&(n.onOpenChange(!0),n.content?.focus(),t.preventDefault())})})})});rc.displayName=ru;var rs="MenuSubContent",rf=a.forwardRef((e,t)=>{let n=nK(nz,e.__scopeMenu),{forceMount:r=n.forceMount,...o}=e,l=nL(nz,e.__scopeMenu),i=nD(nz,e.__scopeMenu),s=ri(rs,e.__scopeMenu),f=a.useRef(null),d=(0,c.s)(t,f);return(0,v.jsx)(nO.Provider,{scope:e.__scopeMenu,children:(0,v.jsx)(tg,{present:r||l.open,children:(0,v.jsx)(nO.Slot,{scope:e.__scopeMenu,children:(0,v.jsx)(n$,{id:s.contentId,"aria-labelledby":s.triggerId,...o,ref:d,align:"start",side:"rtl"===i.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{i.isUsingKeyboardRef.current&&f.current?.focus(),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:u(e.onFocusOutside,e=>{e.target!==s.trigger&&l.onOpenChange(!1)}),onEscapeKeyDown:u(e.onEscapeKeyDown,e=>{i.onClose(),e.preventDefault()}),onKeyDown:u(e.onKeyDown,e=>{let t=e.currentTarget.contains(e.target),n=nP[i.dir].includes(e.key);t&&n&&(l.onOpenChange(!1),s.trigger?.focus(),e.preventDefault())})})})})})});function rd(e){return e?"open":"closed"}function rp(e){return"indeterminate"===e}function rh(e){return rp(e)?"indeterminate":e?"checked":"unchecked"}function ry(e){return t=>"mouse"===t.pointerType?e(t):void 0}rf.displayName=rs;var rv="DropdownMenu",[rm,rg]=(0,s.A)(rv,[nS]),rb=nS(),[rw,rR]=rm(rv),r_=e=>{let{__scopeDropdownMenu:t,children:n,dir:r,open:o,defaultOpen:l,onOpenChange:i,modal:u=!0}=e,c=rb(t),s=a.useRef(null),[f,d]=p({prop:o,defaultProp:l??!1,onChange:i,caller:rv});return(0,v.jsx)(rw,{scope:t,triggerId:B(),triggerRef:s,contentId:B(),open:f,onOpenChange:d,onOpenToggle:a.useCallback(()=>d(e=>!e),[d]),modal:u,children:(0,v.jsx)(nI,{...c,open:f,onOpenChange:d,dir:r,modal:u,children:n})})};r_.displayName=rv;var rE="DropdownMenuTrigger",rP=a.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,disabled:r=!1,...o}=e,l=rR(rE,n),a=rb(n);return(0,v.jsx)(nU,{asChild:!0,...a,children:(0,v.jsx)(h.sG.button,{type:"button",id:l.triggerId,"aria-haspopup":"menu","aria-expanded":l.open,"aria-controls":l.open?l.contentId:void 0,"data-state":l.open?"open":"closed","data-disabled":r?"":void 0,disabled:r,...o,ref:(0,c.t)(t,l.triggerRef),onPointerDown:u(e.onPointerDown,e=>{!r&&0===e.button&&!1===e.ctrlKey&&(l.onOpenToggle(),l.open||e.preventDefault())}),onKeyDown:u(e.onKeyDown,e=>{!r&&(["Enter"," "].includes(e.key)&&l.onOpenToggle(),"ArrowDown"===e.key&&l.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});rP.displayName=rE;var rx=e=>{let{__scopeDropdownMenu:t,...n}=e,r=rb(t);return(0,v.jsx)(nB,{...r,...n})};rx.displayName="DropdownMenuPortal";var rO="DropdownMenuContent",rT=a.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=rR(rO,n),l=rb(n),i=a.useRef(!1);return(0,v.jsx)(nV,{id:o.contentId,"aria-labelledby":o.triggerId,...l,...r,ref:t,onCloseAutoFocus:u(e.onCloseAutoFocus,e=>{i.current||o.triggerRef.current?.focus(),i.current=!1,e.preventDefault()}),onInteractOutside:u(e.onInteractOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey,r=2===t.button||n;(!o.modal||r)&&(i.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});rT.displayName=rO,a.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=rb(n);return(0,v.jsx)(nZ,{...o,...r,ref:t})}).displayName="DropdownMenuGroup",a.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=rb(n);return(0,v.jsx)(nQ,{...o,...r,ref:t})}).displayName="DropdownMenuLabel";var rj=a.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=rb(n);return(0,v.jsx)(n1,{...o,...r,ref:t})});rj.displayName="DropdownMenuItem",a.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=rb(n);return(0,v.jsx)(n6,{...o,...r,ref:t})}).displayName="DropdownMenuCheckboxItem",a.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=rb(n);return(0,v.jsx)(n5,{...o,...r,ref:t})}).displayName="DropdownMenuRadioGroup",a.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=rb(n);return(0,v.jsx)(n8,{...o,...r,ref:t})}).displayName="DropdownMenuRadioItem",a.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=rb(n);return(0,v.jsx)(rr,{...o,...r,ref:t})}).displayName="DropdownMenuItemIndicator",a.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=rb(n);return(0,v.jsx)(ro,{...o,...r,ref:t})}).displayName="DropdownMenuSeparator",a.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=rb(n);return(0,v.jsx)(rl,{...o,...r,ref:t})}).displayName="DropdownMenuArrow",a.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=rb(n);return(0,v.jsx)(rc,{...o,...r,ref:t})}).displayName="DropdownMenuSubTrigger",a.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=rb(n);return(0,v.jsx)(rf,{...o,...r,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})}).displayName="DropdownMenuSubContent";var rM=r_,rS=rP,rC=rx,rA=rT,rN=rj},41500:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"fillLazyItemsTillLeafWithHead",{enumerable:!0,get:function(){return function e(t,n,l,a,i,u,c){if(0===Object.keys(a[1]).length){n.head=u;return}for(let s in a[1]){let f,d=a[1][s],p=d[0],h=(0,r.createRouterCacheKey)(p),y=null!==i&&void 0!==i[2][s]?i[2][s]:null;if(l){let r=l.parallelRoutes.get(s);if(r){let l,a=(null==c?void 0:c.kind)==="auto"&&c.status===o.PrefetchCacheEntryStatus.reusable,i=new Map(r),f=i.get(h);l=null!==y?{lazyData:null,rsc:y[1],prefetchRsc:null,head:null,prefetchHead:null,loading:y[3],parallelRoutes:new Map(null==f?void 0:f.parallelRoutes),navigatedAt:t}:a&&f?{lazyData:f.lazyData,rsc:f.rsc,prefetchRsc:f.prefetchRsc,head:f.head,prefetchHead:f.prefetchHead,parallelRoutes:new Map(f.parallelRoutes),loading:f.loading}:{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map(null==f?void 0:f.parallelRoutes),loading:null,navigatedAt:t},i.set(h,l),e(t,l,f,d,y||null,u,c),n.parallelRoutes.set(s,i);continue}}if(null!==y){let e=y[1],n=y[3];f={lazyData:null,rsc:e,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:n,navigatedAt:t}}else f={lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:t};let v=n.parallelRoutes.get(s);v?v.set(h,f):n.parallelRoutes.set(s,new Map([[h,f]])),e(t,f,void 0,d,y,u,c)}}}});let r=n(33123),o=n(59154);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},44397:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"findHeadInCache",{enumerable:!0,get:function(){return o}});let r=n(33123);function o(e,t){return function e(t,n,o){if(0===Object.keys(n).length)return[t,o];if(n.children){let[l,a]=n.children,i=t.parallelRoutes.get("children");if(i){let t=(0,r.createRouterCacheKey)(l),n=i.get(t);if(n){let r=e(n,a,o+"/"+t);if(r)return r}}}for(let l in n){if("children"===l)continue;let[a,i]=n[l],u=t.parallelRoutes.get(l);if(!u)continue;let c=(0,r.createRouterCacheKey)(a),s=u.get(c);if(!s)continue;let f=e(s,i,o+"/"+c);if(f)return f}return null}(e,t,"")}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},48976:(e,t,n)=>{function r(){throw Object.defineProperty(Error("`forbidden()` is experimental and only allowed to be enabled when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E488",enumerable:!1,configurable:!0})}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"forbidden",{enumerable:!0,get:function(){return r}}),n(8704).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},50593:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{NavigationResultTag:function(){return f},PrefetchPriority:function(){return d},cancelPrefetchTask:function(){return u},createCacheKey:function(){return s},getCurrentCacheVersion:function(){return a},navigate:function(){return o},prefetch:function(){return r},reschedulePrefetchTask:function(){return c},revalidateEntireCache:function(){return l},schedulePrefetchTask:function(){return i}});let n=()=>{throw Object.defineProperty(Error("Segment Cache experiment is not enabled. This is a bug in Next.js."),"__NEXT_ERROR_CODE",{value:"E654",enumerable:!1,configurable:!0})},r=n,o=n,l=n,a=n,i=n,u=n,c=n,s=n;var f=function(e){return e[e.MPA=0]="MPA",e[e.Success=1]="Success",e[e.NoOp=2]="NoOp",e[e.Async=3]="Async",e}({}),d=function(e){return e[e.Intent=2]="Intent",e[e.Default=1]="Default",e[e.Background=0]="Background",e}({});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},51550:(e,t,n)=>{function r(e,t){if(!Object.prototype.hasOwnProperty.call(e,t))throw TypeError("attempted to use private field on non-instance");return e}n.r(t),n.d(t,{_:()=>r})},53038:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return o}});let r=n(43210);function o(e,t){let n=(0,r.useRef)(null),o=(0,r.useRef)(null);return(0,r.useCallback)(r=>{if(null===r){let e=n.current;e&&(n.current=null,e());let t=o.current;t&&(o.current=null,t())}else e&&(n.current=l(e,r)),t&&(o.current=l(t,r))},[e,t])}function l(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let n=e(t);return"function"==typeof n?n:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},53332:(e,t,n)=>{var r=n(43210),o="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},l=r.useState,a=r.useEffect,i=r.useLayoutEffect,u=r.useDebugValue;function c(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!o(e,n)}catch(e){return!0}}var s="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var n=t(),r=l({inst:{value:n,getSnapshot:t}}),o=r[0].inst,s=r[1];return i(function(){o.value=n,o.getSnapshot=t,c(o)&&s({inst:o})},[e,n,t]),a(function(){return c(o)&&s({inst:o}),e(function(){c(o)&&s({inst:o})})},[e]),u(n),n};t.useSyncExternalStore=void 0!==r.useSyncExternalStore?r.useSyncExternalStore:s},54674:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizePathTrailingSlash",{enumerable:!0,get:function(){return l}});let r=n(84949),o=n(19169),l=e=>{if(!e.startsWith("/"))return e;let{pathname:t,query:n,hash:l}=(0,o.parsePath)(e);return""+(0,r.removeTrailingSlash)(t)+n+l};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},56928:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"applyFlightData",{enumerable:!0,get:function(){return l}});let r=n(41500),o=n(33898);function l(e,t,n,l,a){let{tree:i,seedData:u,head:c,isRootRender:s}=l;if(null===u)return!1;if(s){let o=u[1];n.loading=u[3],n.rsc=o,n.prefetchRsc=null,(0,r.fillLazyItemsTillLeafWithHead)(e,n,t,i,u,c,a)}else n.rsc=t.rsc,n.prefetchRsc=t.prefetchRsc,n.parallelRoutes=new Map(t.parallelRoutes),n.loading=t.loading,(0,o.fillCacheWithNewSubTreeData)(e,n,t,l,a);return!0}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},57379:(e,t,n)=>{e.exports=n(53332)},58869:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},58887:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("message-square",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},59435:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"handleMutable",{enumerable:!0,get:function(){return l}});let r=n(70642);function o(e){return void 0!==e}function l(e,t){var n,l;let a=null==(n=t.shouldScroll)||n,i=e.nextUrl;if(o(t.patchedTree)){let n=(0,r.computeChangedPath)(e.tree,t.patchedTree);n?i=n:i||(i=e.canonicalUrl)}return{canonicalUrl:o(t.canonicalUrl)?t.canonicalUrl===e.canonicalUrl?e.canonicalUrl:t.canonicalUrl:e.canonicalUrl,pushRef:{pendingPush:o(t.pendingPush)?t.pendingPush:e.pushRef.pendingPush,mpaNavigation:o(t.mpaNavigation)?t.mpaNavigation:e.pushRef.mpaNavigation,preserveCustomHistoryState:o(t.preserveCustomHistoryState)?t.preserveCustomHistoryState:e.pushRef.preserveCustomHistoryState},focusAndScrollRef:{apply:!!a&&(!!o(null==t?void 0:t.scrollableSegments)||e.focusAndScrollRef.apply),onlyHashChange:t.onlyHashChange||!1,hashFragment:a?t.hashFragment&&""!==t.hashFragment?decodeURIComponent(t.hashFragment.slice(1)):e.focusAndScrollRef.hashFragment:null,segmentPaths:a?null!=(l=null==t?void 0:t.scrollableSegments)?l:e.focusAndScrollRef.segmentPaths:[]},cache:t.cache?t.cache:e.cache,prefetchCache:t.prefetchCache?t.prefetchCache:e.prefetchCache,tree:o(t.patchedTree)?t.patchedTree:e.tree,nextUrl:i}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},59656:(e,t,n)=>{n.r(t),n.d(t,{_:()=>o});var r=0;function o(e){return"__private_"+r+++"_"+e}},61794:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isLocalURL",{enumerable:!0,get:function(){return l}});let r=n(79289),o=n(26736);function l(e){if(!(0,r.isAbsoluteUrl)(e))return!0;try{let t=(0,r.getLocationOrigin)(),n=new URL(e,t);return n.origin===t&&(0,o.hasBasePath)(n.pathname)}catch(e){return!1}}},62688:(e,t,n)=>{n.d(t,{A:()=>f});var r=n(43210);let o=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,n)=>n?n.toUpperCase():t.toLowerCase()),a=e=>{let t=l(e);return t.charAt(0).toUpperCase()+t.slice(1)},i=(...e)=>e.filter((e,t,n)=>!!e&&""!==e.trim()&&n.indexOf(e)===t).join(" ").trim(),u=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let s=(0,r.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:n=2,absoluteStrokeWidth:o,className:l="",children:a,iconNode:s,...f},d)=>(0,r.createElement)("svg",{ref:d,...c,width:t,height:t,stroke:e,strokeWidth:o?24*Number(n)/Number(t):n,className:i("lucide",l),...!a&&!u(f)&&{"aria-hidden":"true"},...f},[...s.map(([e,t])=>(0,r.createElement)(e,t)),...Array.isArray(a)?a:[a]])),f=(e,t)=>{let n=(0,r.forwardRef)(({className:n,...l},u)=>(0,r.createElement)(s,{ref:u,iconNode:t,className:i(`lucide-${o(a(e))}`,`lucide-${e}`,n),...l}));return n.displayName=a(e),n}},62765:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"notFound",{enumerable:!0,get:function(){return o}});let r=""+n(8704).HTTP_ERROR_FALLBACK_ERROR_CODE+";404";function o(){let e=Object.defineProperty(Error(r),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});throw e.digest=r,e}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},63690:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{createMutableActionQueue:function(){return h},dispatchNavigateAction:function(){return m},dispatchTraverseAction:function(){return g},getCurrentAppRouterState:function(){return y},publicAppRouterInstance:function(){return b}});let r=n(59154),o=n(8830),l=n(43210),a=n(91992);n(50593);let i=n(19129),u=n(96127),c=n(89752),s=n(75076),f=n(73406);function d(e,t){null!==e.pending&&(e.pending=e.pending.next,null!==e.pending?p({actionQueue:e,action:e.pending,setState:t}):e.needsRefresh&&(e.needsRefresh=!1,e.dispatch({type:r.ACTION_REFRESH,origin:window.location.origin},t)))}async function p(e){let{actionQueue:t,action:n,setState:r}=e,o=t.state;t.pending=n;let l=n.payload,i=t.action(o,l);function u(e){n.discarded||(t.state=e,d(t,r),n.resolve(e))}(0,a.isThenable)(i)?i.then(u,e=>{d(t,r),n.reject(e)}):u(i)}function h(e,t){let n={state:e,dispatch:(e,t)=>(function(e,t,n){let o={resolve:n,reject:()=>{}};if(t.type!==r.ACTION_RESTORE){let e=new Promise((e,t)=>{o={resolve:e,reject:t}});(0,l.startTransition)(()=>{n(e)})}let a={payload:t,next:null,resolve:o.resolve,reject:o.reject};null===e.pending?(e.last=a,p({actionQueue:e,action:a,setState:n})):t.type===r.ACTION_NAVIGATE||t.type===r.ACTION_RESTORE?(e.pending.discarded=!0,a.next=e.pending.next,e.pending.payload.type===r.ACTION_SERVER_ACTION&&(e.needsRefresh=!0),p({actionQueue:e,action:a,setState:n})):(null!==e.last&&(e.last.next=a),e.last=a)})(n,e,t),action:async(e,t)=>(0,o.reducer)(e,t),pending:null,last:null,onRouterTransitionStart:null!==t&&"function"==typeof t.onRouterTransitionStart?t.onRouterTransitionStart:null};return n}function y(){return null}function v(){return null}function m(e,t,n,o){let l=new URL((0,u.addBasePath)(e),location.href);(0,f.setLinkForCurrentNavigation)(o);(0,i.dispatchAppRouterAction)({type:r.ACTION_NAVIGATE,url:l,isExternalUrl:(0,c.isExternalURL)(l),locationSearch:location.search,shouldScroll:n,navigateType:t,allowAliasing:!0})}function g(e,t){(0,i.dispatchAppRouterAction)({type:r.ACTION_RESTORE,url:new URL(e),tree:t})}let b={back:()=>window.history.back(),forward:()=>window.history.forward(),prefetch:(e,t)=>{let n=function(){throw Object.defineProperty(Error("Internal Next.js error: Router action dispatched before initialization."),"__NEXT_ERROR_CODE",{value:"E668",enumerable:!1,configurable:!0})}(),o=(0,c.createPrefetchURL)(e);if(null!==o){var l;(0,s.prefetchReducer)(n.state,{type:r.ACTION_PREFETCH,url:o,kind:null!=(l=null==t?void 0:t.kind)?l:r.PrefetchKind.FULL})}},replace:(e,t)=>{(0,l.startTransition)(()=>{var n;m(e,"replace",null==(n=null==t?void 0:t.scroll)||n,null)})},push:(e,t)=>{(0,l.startTransition)(()=>{var n;m(e,"push",null==(n=null==t?void 0:t.scroll)||n,null)})},refresh:()=>{(0,l.startTransition)(()=>{(0,i.dispatchAppRouterAction)({type:r.ACTION_REFRESH,origin:window.location.origin})})},hmrRefresh:()=>{throw Object.defineProperty(Error("hmrRefresh can only be used in development mode. Please use refresh instead."),"__NEXT_ERROR_CODE",{value:"E485",enumerable:!1,configurable:!0})}};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},65951:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"shouldHardNavigate",{enumerable:!0,get:function(){return function e(t,n){let[l,a]=n,[i,u]=t;return(0,o.matchSegment)(i,l)?!(t.length<=2)&&e((0,r.getNextFlightSegmentPath)(t),a[u]):!!Array.isArray(i)}}});let r=n(74007),o=n(14077);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},65956:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{abortTask:function(){return h},listenForDynamicRequest:function(){return p},startPPRNavigation:function(){return c},updateCacheNodeOnPopstateRestoration:function(){return function e(t,n){let r=n[1],o=t.parallelRoutes,a=new Map(o);for(let t in r){let n=r[t],i=n[0],u=(0,l.createRouterCacheKey)(i),c=o.get(t);if(void 0!==c){let r=c.get(u);if(void 0!==r){let o=e(r,n),l=new Map(c);l.set(u,o),a.set(t,l)}}}let i=t.rsc,u=m(i)&&"pending"===i.status;return{lazyData:null,rsc:i,head:t.head,prefetchHead:u?t.prefetchHead:[null,null],prefetchRsc:u?t.prefetchRsc:null,loading:t.loading,parallelRoutes:a,navigatedAt:t.navigatedAt}}}});let r=n(83913),o=n(14077),l=n(33123),a=n(2030),i=n(5334),u={route:null,node:null,dynamicRequestTree:null,children:null};function c(e,t,n,a,i,c,d,p,h){return function e(t,n,a,i,c,d,p,h,y,v,m){let g=a[1],b=i[1],w=null!==d?d[2]:null;c||!0===i[4]&&(c=!0);let R=n.parallelRoutes,_=new Map(R),E={},P=null,x=!1,O={};for(let n in b){let a,i=b[n],f=g[n],d=R.get(n),T=null!==w?w[n]:null,j=i[0],M=v.concat([n,j]),S=(0,l.createRouterCacheKey)(j),C=void 0!==f?f[0]:void 0,A=void 0!==d?d.get(S):void 0;if(null!==(a=j===r.DEFAULT_SEGMENT_KEY?void 0!==f?{route:f,node:null,dynamicRequestTree:null,children:null}:s(t,f,i,A,c,void 0!==T?T:null,p,h,M,m):y&&0===Object.keys(i[1]).length?s(t,f,i,A,c,void 0!==T?T:null,p,h,M,m):void 0!==f&&void 0!==C&&(0,o.matchSegment)(j,C)&&void 0!==A&&void 0!==f?e(t,A,f,i,c,T,p,h,y,M,m):s(t,f,i,A,c,void 0!==T?T:null,p,h,M,m))){if(null===a.route)return u;null===P&&(P=new Map),P.set(n,a);let e=a.node;if(null!==e){let t=new Map(d);t.set(S,e),_.set(n,t)}let t=a.route;E[n]=t;let r=a.dynamicRequestTree;null!==r?(x=!0,O[n]=r):O[n]=t}else E[n]=i,O[n]=i}if(null===P)return null;let T={lazyData:null,rsc:n.rsc,prefetchRsc:n.prefetchRsc,head:n.head,prefetchHead:n.prefetchHead,loading:n.loading,parallelRoutes:_,navigatedAt:t};return{route:f(i,E),node:T,dynamicRequestTree:x?f(i,O):null,children:P}}(e,t,n,a,!1,i,c,d,p,[],h)}function s(e,t,n,r,o,c,s,p,h,y){return!o&&(void 0===t||(0,a.isNavigatingToNewRootLayout)(t,n))?u:function e(t,n,r,o,a,u,c,s){let p,h,y,v,m=n[1],g=0===Object.keys(m).length;if(void 0!==r&&r.navigatedAt+i.DYNAMIC_STALETIME_MS>t)p=r.rsc,h=r.loading,y=r.head,v=r.navigatedAt;else if(null===o)return d(t,n,null,a,u,c,s);else if(p=o[1],h=o[3],y=g?a:null,v=t,o[4]||u&&g)return d(t,n,o,a,u,c,s);let b=null!==o?o[2]:null,w=new Map,R=void 0!==r?r.parallelRoutes:null,_=new Map(R),E={},P=!1;if(g)s.push(c);else for(let n in m){let r=m[n],o=null!==b?b[n]:null,i=null!==R?R.get(n):void 0,f=r[0],d=c.concat([n,f]),p=(0,l.createRouterCacheKey)(f),h=e(t,r,void 0!==i?i.get(p):void 0,o,a,u,d,s);w.set(n,h);let y=h.dynamicRequestTree;null!==y?(P=!0,E[n]=y):E[n]=r;let v=h.node;if(null!==v){let e=new Map;e.set(p,v),_.set(n,e)}}return{route:n,node:{lazyData:null,rsc:p,prefetchRsc:null,head:y,prefetchHead:null,loading:h,parallelRoutes:_,navigatedAt:v},dynamicRequestTree:P?f(n,E):null,children:w}}(e,n,r,c,s,p,h,y)}function f(e,t){let n=[e[0],t];return 2 in e&&(n[2]=e[2]),3 in e&&(n[3]=e[3]),4 in e&&(n[4]=e[4]),n}function d(e,t,n,r,o,a,i){let u=f(t,t[1]);return u[3]="refetch",{route:t,node:function e(t,n,r,o,a,i,u){let c=n[1],s=null!==r?r[2]:null,f=new Map;for(let n in c){let r=c[n],d=null!==s?s[n]:null,p=r[0],h=i.concat([n,p]),y=(0,l.createRouterCacheKey)(p),v=e(t,r,void 0===d?null:d,o,a,h,u),m=new Map;m.set(y,v),f.set(n,m)}let d=0===f.size;d&&u.push(i);let p=null!==r?r[1]:null,h=null!==r?r[3]:null;return{lazyData:null,parallelRoutes:f,prefetchRsc:void 0!==p?p:null,prefetchHead:d?o:[null,null],loading:void 0!==h?h:null,rsc:g(),head:d?g():null,navigatedAt:t}}(e,t,n,r,o,a,i),dynamicRequestTree:u,children:null}}function p(e,t){t.then(t=>{let{flightData:n}=t;if("string"!=typeof n){for(let t of n){let{segmentPath:n,tree:r,seedData:a,head:i}=t;a&&function(e,t,n,r,a){let i=e;for(let e=0;e<t.length;e+=2){let n=t[e],r=t[e+1],l=i.children;if(null!==l){let e=l.get(n);if(void 0!==e){let t=e.route[0];if((0,o.matchSegment)(r,t)){i=e;continue}}}return}!function e(t,n,r,a){if(null===t.dynamicRequestTree)return;let i=t.children,u=t.node;if(null===i){null!==u&&(function e(t,n,r,a,i){let u=n[1],c=r[1],s=a[2],f=t.parallelRoutes;for(let t in u){let n=u[t],r=c[t],a=s[t],d=f.get(t),p=n[0],h=(0,l.createRouterCacheKey)(p),v=void 0!==d?d.get(h):void 0;void 0!==v&&(void 0!==r&&(0,o.matchSegment)(p,r[0])&&null!=a?e(v,n,r,a,i):y(n,v,null))}let d=t.rsc,p=a[1];null===d?t.rsc=p:m(d)&&d.resolve(p);let h=t.head;m(h)&&h.resolve(i)}(u,t.route,n,r,a),t.dynamicRequestTree=null);return}let c=n[1],s=r[2];for(let t in n){let n=c[t],r=s[t],l=i.get(t);if(void 0!==l){let t=l.route[0];if((0,o.matchSegment)(n[0],t)&&null!=r)return e(l,n,r,a)}}}(i,n,r,a)}(e,n,r,a,i)}h(e,null)}},t=>{h(e,t)})}function h(e,t){let n=e.node;if(null===n)return;let r=e.children;if(null===r)y(e.route,n,t);else for(let e of r.values())h(e,t);e.dynamicRequestTree=null}function y(e,t,n){let r=e[1],o=t.parallelRoutes;for(let e in r){let t=r[e],a=o.get(e);if(void 0===a)continue;let i=t[0],u=(0,l.createRouterCacheKey)(i),c=a.get(u);void 0!==c&&y(t,c,n)}let a=t.rsc;m(a)&&(null===n?a.resolve(null):a.reject(n));let i=t.head;m(i)&&i.resolve(null)}let v=Symbol();function m(e){return e&&e.tag===v}function g(){let e,t,n=new Promise((n,r)=>{e=n,t=r});return n.status="pending",n.resolve=t=>{"pending"===n.status&&(n.status="fulfilled",n.value=t,e(t))},n.reject=e=>{"pending"===n.status&&(n.status="rejected",n.reason=e,t(e))},n.tag=v,n}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},66156:(e,t,n)=>{n.d(t,{N:()=>o});var r=n(43210),o=globalThis?.document?r.useLayoutEffect:()=>{}},70642:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{computeChangedPath:function(){return s},extractPathFromFlightRouterState:function(){return c},getSelectedParams:function(){return function e(t,n){for(let r of(void 0===n&&(n={}),Object.values(t[1]))){let t=r[0],l=Array.isArray(t),a=l?t[1]:t;!a||a.startsWith(o.PAGE_SEGMENT_KEY)||(l&&("c"===t[2]||"oc"===t[2])?n[t[0]]=t[1].split("/"):l&&(n[t[0]]=t[1]),n=e(r,n))}return n}}});let r=n(72859),o=n(83913),l=n(14077),a=e=>"/"===e[0]?e.slice(1):e,i=e=>"string"==typeof e?"children"===e?"":e:e[1];function u(e){return e.reduce((e,t)=>""===(t=a(t))||(0,o.isGroupSegment)(t)?e:e+"/"+t,"")||"/"}function c(e){var t;let n=Array.isArray(e[0])?e[0][1]:e[0];if(n===o.DEFAULT_SEGMENT_KEY||r.INTERCEPTION_ROUTE_MARKERS.some(e=>n.startsWith(e)))return;if(n.startsWith(o.PAGE_SEGMENT_KEY))return"";let l=[i(n)],a=null!=(t=e[1])?t:{},s=a.children?c(a.children):void 0;if(void 0!==s)l.push(s);else for(let[e,t]of Object.entries(a)){if("children"===e)continue;let n=c(t);void 0!==n&&l.push(n)}return u(l)}function s(e,t){let n=function e(t,n){let[o,a]=t,[u,s]=n,f=i(o),d=i(u);if(r.INTERCEPTION_ROUTE_MARKERS.some(e=>f.startsWith(e)||d.startsWith(e)))return"";if(!(0,l.matchSegment)(o,u)){var p;return null!=(p=c(n))?p:""}for(let t in a)if(s[t]){let n=e(a[t],s[t]);if(null!==n)return i(u)+"/"+n}return null}(e,t);return null==n||"/"===n?n:u(n.split("/"))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},70899:(e,t,n)=>{function r(){throw Object.defineProperty(Error("`unauthorized()` is experimental and only allowed to be used when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E411",enumerable:!1,configurable:!0})}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unauthorized",{enumerable:!0,get:function(){return r}}),n(8704).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},71042:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unstable_rethrow",{enumerable:!0,get:function(){return function e(t){if((0,a.isNextRouterError)(t)||(0,l.isBailoutToCSRError)(t)||(0,u.isDynamicServerError)(t)||(0,i.isDynamicPostpone)(t)||(0,o.isPostpone)(t)||(0,r.isHangingPromiseRejectionError)(t))throw t;t instanceof Error&&"cause"in t&&e(t.cause)}}});let r=n(68388),o=n(52637),l=n(51846),a=n(31162),i=n(84971),u=n(98479);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},73406:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{IDLE_LINK_STATUS:function(){return c},PENDING_LINK_STATUS:function(){return u},mountFormInstance:function(){return g},mountLinkInstance:function(){return m},onLinkVisibilityChanged:function(){return w},onNavigationIntent:function(){return R},pingVisibleLinks:function(){return E},setLinkForCurrentNavigation:function(){return s},unmountLinkForCurrentNavigation:function(){return f},unmountPrefetchableInstance:function(){return b}}),n(63690);let r=n(89752),o=n(59154),l=n(50593),a=n(43210),i=null,u={pending:!0},c={pending:!1};function s(e){(0,a.startTransition)(()=>{null==i||i.setOptimisticLinkStatus(c),null==e||e.setOptimisticLinkStatus(u),i=e})}function f(e){i===e&&(i=null)}let d="function"==typeof WeakMap?new WeakMap:new Map,p=new Set,h="function"==typeof IntersectionObserver?new IntersectionObserver(function(e){for(let t of e){let e=t.intersectionRatio>0;w(t.target,e)}},{rootMargin:"200px"}):null;function y(e,t){void 0!==d.get(e)&&b(e),d.set(e,t),null!==h&&h.observe(e)}function v(e){try{return(0,r.createPrefetchURL)(e)}catch(t){return("function"==typeof reportError?reportError:console.error)("Cannot prefetch '"+e+"' because it cannot be converted to a URL."),null}}function m(e,t,n,r,o,l){if(o){let o=v(t);if(null!==o){let t={router:n,kind:r,isVisible:!1,wasHoveredOrTouched:!1,prefetchTask:null,cacheVersion:-1,prefetchHref:o.href,setOptimisticLinkStatus:l};return y(e,t),t}}return{router:n,kind:r,isVisible:!1,wasHoveredOrTouched:!1,prefetchTask:null,cacheVersion:-1,prefetchHref:null,setOptimisticLinkStatus:l}}function g(e,t,n,r){let o=v(t);null!==o&&y(e,{router:n,kind:r,isVisible:!1,wasHoveredOrTouched:!1,prefetchTask:null,cacheVersion:-1,prefetchHref:o.href,setOptimisticLinkStatus:null})}function b(e){let t=d.get(e);if(void 0!==t){d.delete(e),p.delete(t);let n=t.prefetchTask;null!==n&&(0,l.cancelPrefetchTask)(n)}null!==h&&h.unobserve(e)}function w(e,t){let n=d.get(e);void 0!==n&&(n.isVisible=t,t?p.add(n):p.delete(n),_(n))}function R(e,t){let n=d.get(e);void 0!==n&&void 0!==n&&(n.wasHoveredOrTouched=!0,_(n))}function _(e){let t=e.prefetchTask;if(!e.isVisible){null!==t&&(0,l.cancelPrefetchTask)(t);return}}function E(e,t){let n=(0,l.getCurrentCacheVersion)();for(let r of p){let a=r.prefetchTask;if(null!==a&&r.cacheVersion===n&&a.key.nextUrl===e&&a.treeAtTimeOfPrefetch===t)continue;null!==a&&(0,l.cancelPrefetchTask)(a);let i=(0,l.createCacheKey)(r.prefetchHref,e),u=r.wasHoveredOrTouched?l.PrefetchPriority.Intent:l.PrefetchPriority.Default;r.prefetchTask=(0,l.schedulePrefetchTask)(i,t,r.kind===o.PrefetchKind.FULL,u),r.cacheVersion=(0,l.getCurrentCacheVersion)()}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},75076:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{prefetchQueue:function(){return l},prefetchReducer:function(){return a}});let r=n(5144),o=n(5334),l=new r.PromiseQueue(5),a=function(e,t){(0,o.prunePrefetchCache)(e.prefetchCache);let{url:n}=t;return(0,o.getOrCreatePrefetchCacheEntry)({url:n,nextUrl:e.nextUrl,prefetchCache:e.prefetchCache,kind:t.kind,tree:e.tree,allowAliasing:!0}),e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},76715:(e,t)=>{function n(e){let t={};for(let[n,r]of e.entries()){let e=t[n];void 0===e?t[n]=r:Array.isArray(e)?e.push(r):t[n]=[e,r]}return t}function r(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function o(e){let t=new URLSearchParams;for(let[n,o]of Object.entries(e))if(Array.isArray(o))for(let e of o)t.append(n,r(e));else t.set(n,r(o));return t}function l(e){for(var t=arguments.length,n=Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];for(let t of n){for(let n of t.keys())e.delete(n);for(let[n,r]of t.entries())e.append(n,r)}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{assign:function(){return l},searchParamsToUrlQuery:function(){return n},urlQueryToSearchParams:function(){return o}})},77022:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"AppRouterAnnouncer",{enumerable:!0,get:function(){return a}});let r=n(43210),o=n(51215),l="next-route-announcer";function a(e){let{tree:t}=e,[n,a]=(0,r.useState)(null);(0,r.useEffect)(()=>(a(function(){var e;let t=document.getElementsByName(l)[0];if(null==t||null==(e=t.shadowRoot)?void 0:e.childNodes[0])return t.shadowRoot.childNodes[0];{let e=document.createElement(l);e.style.cssText="position:absolute";let t=document.createElement("div");return t.ariaLive="assertive",t.id="__next-route-announcer__",t.role="alert",t.style.cssText="position:absolute;border:0;height:1px;margin:-1px;padding:0;width:1px;clip:rect(0 0 0 0);overflow:hidden;white-space:nowrap;word-wrap:normal",e.attachShadow({mode:"open"}).appendChild(t),document.body.appendChild(e),t}}()),()=>{let e=document.getElementsByTagName(l)[0];(null==e?void 0:e.isConnected)&&document.body.removeChild(e)}),[]);let[i,u]=(0,r.useState)(""),c=(0,r.useRef)(void 0);return(0,r.useEffect)(()=>{let e="";if(document.title)e=document.title;else{let t=document.querySelector("h1");t&&(e=t.innerText||t.textContent||"")}void 0!==c.current&&c.current!==e&&u(e),c.current=e},[t]),n?(0,o.createPortal)(i,n):null}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},78272:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},78866:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"refreshReducer",{enumerable:!0,get:function(){return h}});let r=n(59008),o=n(57391),l=n(86770),a=n(2030),i=n(25232),u=n(59435),c=n(41500),s=n(89752),f=n(96493),d=n(68214),p=n(22308);function h(e,t){let{origin:n}=t,h={},y=e.canonicalUrl,v=e.tree;h.preserveCustomHistoryState=!1;let m=(0,s.createEmptyCacheNode)(),g=(0,d.hasInterceptionRouteInCurrentTree)(e.tree);m.lazyData=(0,r.fetchServerResponse)(new URL(y,n),{flightRouterState:[v[0],v[1],v[2],"refetch"],nextUrl:g?e.nextUrl:null});let b=Date.now();return m.lazyData.then(async n=>{let{flightData:r,canonicalUrl:s}=n;if("string"==typeof r)return(0,i.handleExternalUrl)(e,h,r,e.pushRef.pendingPush);for(let n of(m.lazyData=null,r)){let{tree:r,seedData:u,head:d,isRootRender:w}=n;if(!w)return console.log("REFRESH FAILED"),e;let R=(0,l.applyRouterStatePatchToTree)([""],v,r,e.canonicalUrl);if(null===R)return(0,f.handleSegmentMismatch)(e,t,r);if((0,a.isNavigatingToNewRootLayout)(v,R))return(0,i.handleExternalUrl)(e,h,y,e.pushRef.pendingPush);let _=s?(0,o.createHrefFromUrl)(s):void 0;if(s&&(h.canonicalUrl=_),null!==u){let e=u[1],t=u[3];m.rsc=e,m.prefetchRsc=null,m.loading=t,(0,c.fillLazyItemsTillLeafWithHead)(b,m,void 0,r,u,d,void 0),h.prefetchCache=new Map}await (0,p.refreshInactiveParallelSegments)({navigatedAt:b,state:e,updatedTree:R,updatedCache:m,includeNextUrl:g,canonicalUrl:h.canonicalUrl||e.canonicalUrl}),h.cache=m,h.patchedTree=R,v=R}return(0,u.handleMutable)(e,h)},()=>e)}n(50593),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},79289:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{DecodeError:function(){return h},MiddlewareNotFoundError:function(){return g},MissingStaticPage:function(){return m},NormalizeError:function(){return y},PageNotFoundError:function(){return v},SP:function(){return d},ST:function(){return p},WEB_VITALS:function(){return n},execOnce:function(){return r},getDisplayName:function(){return u},getLocationOrigin:function(){return a},getURL:function(){return i},isAbsoluteUrl:function(){return l},isResSent:function(){return c},loadGetInitialProps:function(){return f},normalizeRepeatedSlashes:function(){return s},stringifyError:function(){return b}});let n=["CLS","FCP","FID","INP","LCP","TTFB"];function r(e){let t,n=!1;return function(){for(var r=arguments.length,o=Array(r),l=0;l<r;l++)o[l]=arguments[l];return n||(n=!0,t=e(...o)),t}}let o=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,l=e=>o.test(e);function a(){let{protocol:e,hostname:t,port:n}=window.location;return e+"//"+t+(n?":"+n:"")}function i(){let{href:e}=window.location,t=a();return e.substring(t.length)}function u(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function c(e){return e.finished||e.headersSent}function s(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function f(e,t){let n=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await f(t.Component,t.ctx)}:{};let r=await e.getInitialProps(t);if(n&&c(n))return r;if(!r)throw Object.defineProperty(Error('"'+u(e)+'.getInitialProps()" should resolve to an object. But found "'+r+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return r}let d="undefined"!=typeof performance,p=d&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class h extends Error{}class y extends Error{}class v extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class m extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class g extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function b(e){return JSON.stringify({message:e.message,stack:e.stack})}},82570:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("folder",[["path",{d:"M20 20a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.9a2 2 0 0 1-1.69-.9L9.6 3.9A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13a2 2 0 0 0 2 2Z",key:"1kt360"}]])},84949:(e,t)=>{function n(e){return e.replace(/\/$/,"")||"/"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removeTrailingSlash",{enumerable:!0,get:function(){return n}})},85814:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{default:function(){return v},useLinkStatus:function(){return g}});let r=n(40740),o=n(60687),l=r._(n(43210)),a=n(30195),i=n(22142),u=n(59154),c=n(53038),s=n(79289),f=n(96127);n(50148);let d=n(73406),p=n(61794),h=n(63690);function y(e){return"string"==typeof e?e:(0,a.formatUrl)(e)}function v(e){let t,n,r,[a,v]=(0,l.useOptimistic)(d.IDLE_LINK_STATUS),g=(0,l.useRef)(null),{href:b,as:w,children:R,prefetch:_=null,passHref:E,replace:P,shallow:x,scroll:O,onClick:T,onMouseEnter:j,onTouchStart:M,legacyBehavior:S=!1,onNavigate:C,ref:A,unstable_dynamicOnHover:N,...L}=e;t=R,S&&("string"==typeof t||"number"==typeof t)&&(t=(0,o.jsx)("a",{children:t}));let k=l.default.useContext(i.AppRouterContext),D=!1!==_,I=null===_?u.PrefetchKind.AUTO:u.PrefetchKind.FULL,{href:U,as:F}=l.default.useMemo(()=>{let e=y(b);return{href:e,as:w?y(w):e}},[b,w]);S&&(n=l.default.Children.only(t));let H=S?n&&"object"==typeof n&&n.ref:A,K=l.default.useCallback(e=>(null!==k&&(g.current=(0,d.mountLinkInstance)(e,U,k,I,D,v)),()=>{g.current&&((0,d.unmountLinkForCurrentNavigation)(g.current),g.current=null),(0,d.unmountPrefetchableInstance)(e)}),[D,U,k,I,v]),B={ref:(0,c.useMergedRef)(K,H),onClick(e){S||"function"!=typeof T||T(e),S&&n.props&&"function"==typeof n.props.onClick&&n.props.onClick(e),k&&(e.defaultPrevented||function(e,t,n,r,o,a,i){let{nodeName:u}=e.currentTarget;if(!("A"===u.toUpperCase()&&function(e){let t=e.currentTarget.getAttribute("target");return t&&"_self"!==t||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||e.currentTarget.hasAttribute("download"))){if(!(0,p.isLocalURL)(t)){o&&(e.preventDefault(),location.replace(t));return}e.preventDefault(),l.default.startTransition(()=>{if(i){let e=!1;if(i({preventDefault:()=>{e=!0}}),e)return}(0,h.dispatchNavigateAction)(n||t,o?"replace":"push",null==a||a,r.current)})}}(e,U,F,g,P,O,C))},onMouseEnter(e){S||"function"!=typeof j||j(e),S&&n.props&&"function"==typeof n.props.onMouseEnter&&n.props.onMouseEnter(e),k&&D&&(0,d.onNavigationIntent)(e.currentTarget,!0===N)},onTouchStart:function(e){S||"function"!=typeof M||M(e),S&&n.props&&"function"==typeof n.props.onTouchStart&&n.props.onTouchStart(e),k&&D&&(0,d.onNavigationIntent)(e.currentTarget,!0===N)}};return(0,s.isAbsoluteUrl)(F)?B.href=F:S&&!E&&("a"!==n.type||"href"in n.props)||(B.href=(0,f.addBasePath)(F)),r=S?l.default.cloneElement(n,B):(0,o.jsx)("a",{...L,...B,children:t}),(0,o.jsx)(m.Provider,{value:a,children:r})}n(32708);let m=(0,l.createContext)(d.IDLE_LINK_STATUS),g=()=>(0,l.useContext)(m);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},86770:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"applyRouterStatePatchToTree",{enumerable:!0,get:function(){return function e(t,n,r,u){let c,[s,f,d,p,h]=n;if(1===t.length){let e=i(n,r);return(0,a.addRefreshMarkerToActiveParallelSegments)(e,u),e}let[y,v]=t;if(!(0,l.matchSegment)(y,s))return null;if(2===t.length)c=i(f[v],r);else if(null===(c=e((0,o.getNextFlightSegmentPath)(t),f[v],r,u)))return null;let m=[t[0],{...f,[v]:c},d,p];return h&&(m[4]=!0),(0,a.addRefreshMarkerToActiveParallelSegments)(m,u),m}}});let r=n(83913),o=n(74007),l=n(14077),a=n(22308);function i(e,t){let[n,o]=e,[a,u]=t;if(a===r.DEFAULT_SEGMENT_KEY&&n!==r.DEFAULT_SEGMENT_KEY)return e;if((0,l.matchSegment)(n,a)){let t={};for(let e in o)void 0!==u[e]?t[e]=i(o[e],u[e]):t[e]=o[e];for(let e in u)t[e]||(t[e]=u[e]);let r=[n,t];return e[2]&&(r[2]=e[2]),e[3]&&(r[3]=e[3]),e[4]&&(r[4]=e[4]),r}return t}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},86897:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{getRedirectError:function(){return a},getRedirectStatusCodeFromError:function(){return f},getRedirectTypeFromError:function(){return s},getURLFromRedirectError:function(){return c},permanentRedirect:function(){return u},redirect:function(){return i}});let r=n(52836),o=n(49026),l=n(19121).actionAsyncStorage;function a(e,t,n){void 0===n&&(n=r.RedirectStatusCode.TemporaryRedirect);let l=Object.defineProperty(Error(o.REDIRECT_ERROR_CODE),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return l.digest=o.REDIRECT_ERROR_CODE+";"+t+";"+e+";"+n+";",l}function i(e,t){var n;throw null!=t||(t=(null==l||null==(n=l.getStore())?void 0:n.isAction)?o.RedirectType.push:o.RedirectType.replace),a(e,t,r.RedirectStatusCode.TemporaryRedirect)}function u(e,t){throw void 0===t&&(t=o.RedirectType.replace),a(e,t,r.RedirectStatusCode.PermanentRedirect)}function c(e){return(0,o.isRedirectError)(e)?e.digest.split(";").slice(2,-2).join(";"):null}function s(e){if(!(0,o.isRedirectError)(e))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return e.digest.split(";",2)[1]}function f(e){if(!(0,o.isRedirectError)(e))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return Number(e.digest.split(";").at(-2))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},88233:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},89752:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{createEmptyCacheNode:function(){return M},createPrefetchURL:function(){return T},default:function(){return N},isExternalURL:function(){return O}});let r=n(40740),o=n(60687),l=r._(n(43210)),a=n(22142),i=n(59154),u=n(57391),c=n(10449),s=n(19129),f=r._(n(35656)),d=n(35416),p=n(96127),h=n(77022),y=n(67086),v=n(44397),m=n(89330),g=n(25942),b=n(26736),w=n(70642),R=n(12776),_=n(63690),E=n(36875),P=n(97860);n(73406);let x={};function O(e){return e.origin!==window.location.origin}function T(e){let t;if((0,d.isBot)(window.navigator.userAgent))return null;try{t=new URL((0,p.addBasePath)(e),window.location.href)}catch(t){throw Object.defineProperty(Error("Cannot prefetch '"+e+"' because it cannot be converted to a URL."),"__NEXT_ERROR_CODE",{value:"E234",enumerable:!1,configurable:!0})}return O(t)?null:t}function j(e){let{appRouterState:t}=e;return(0,l.useInsertionEffect)(()=>{let{tree:e,pushRef:n,canonicalUrl:r}=t,o={...n.preserveCustomHistoryState?window.history.state:{},__NA:!0,__PRIVATE_NEXTJS_INTERNALS_TREE:e};n.pendingPush&&(0,u.createHrefFromUrl)(new URL(window.location.href))!==r?(n.pendingPush=!1,window.history.pushState(o,"",r)):window.history.replaceState(o,"",r)},[t]),(0,l.useEffect)(()=>{},[t.nextUrl,t.tree]),null}function M(){return{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1}}function S(e){null==e&&(e={});let t=window.history.state,n=null==t?void 0:t.__NA;n&&(e.__NA=n);let r=null==t?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE;return r&&(e.__PRIVATE_NEXTJS_INTERNALS_TREE=r),e}function C(e){let{headCacheNode:t}=e,n=null!==t?t.head:null,r=null!==t?t.prefetchHead:null,o=null!==r?r:n;return(0,l.useDeferredValue)(n,o)}function A(e){let t,{actionQueue:n,assetPrefix:r,globalError:u}=e,d=(0,s.useActionQueue)(n),{canonicalUrl:p}=d,{searchParams:R,pathname:O}=(0,l.useMemo)(()=>{let e=new URL(p,"http://n");return{searchParams:e.searchParams,pathname:(0,b.hasBasePath)(e.pathname)?(0,g.removeBasePath)(e.pathname):e.pathname}},[p]);(0,l.useEffect)(()=>{function e(e){var t;e.persisted&&(null==(t=window.history.state)?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE)&&(x.pendingMpaPath=void 0,(0,s.dispatchAppRouterAction)({type:i.ACTION_RESTORE,url:new URL(window.location.href),tree:window.history.state.__PRIVATE_NEXTJS_INTERNALS_TREE}))}return window.addEventListener("pageshow",e),()=>{window.removeEventListener("pageshow",e)}},[]),(0,l.useEffect)(()=>{function e(e){let t="reason"in e?e.reason:e.error;if((0,P.isRedirectError)(t)){e.preventDefault();let n=(0,E.getURLFromRedirectError)(t);(0,E.getRedirectTypeFromError)(t)===P.RedirectType.push?_.publicAppRouterInstance.push(n,{}):_.publicAppRouterInstance.replace(n,{})}}return window.addEventListener("error",e),window.addEventListener("unhandledrejection",e),()=>{window.removeEventListener("error",e),window.removeEventListener("unhandledrejection",e)}},[]);let{pushRef:T}=d;if(T.mpaNavigation){if(x.pendingMpaPath!==p){let e=window.location;T.pendingPush?e.assign(p):e.replace(p),x.pendingMpaPath=p}(0,l.use)(m.unresolvedThenable)}(0,l.useEffect)(()=>{let e=window.history.pushState.bind(window.history),t=window.history.replaceState.bind(window.history),n=e=>{var t;let n=window.location.href,r=null==(t=window.history.state)?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE;(0,l.startTransition)(()=>{(0,s.dispatchAppRouterAction)({type:i.ACTION_RESTORE,url:new URL(null!=e?e:n,n),tree:r})})};window.history.pushState=function(t,r,o){return(null==t?void 0:t.__NA)||(null==t?void 0:t._N)||(t=S(t),o&&n(o)),e(t,r,o)},window.history.replaceState=function(e,r,o){return(null==e?void 0:e.__NA)||(null==e?void 0:e._N)||(e=S(e),o&&n(o)),t(e,r,o)};let r=e=>{if(e.state){if(!e.state.__NA)return void window.location.reload();(0,l.startTransition)(()=>{(0,_.dispatchTraverseAction)(window.location.href,e.state.__PRIVATE_NEXTJS_INTERNALS_TREE)})}};return window.addEventListener("popstate",r),()=>{window.history.pushState=e,window.history.replaceState=t,window.removeEventListener("popstate",r)}},[]);let{cache:M,tree:A,nextUrl:N,focusAndScrollRef:L}=d,k=(0,l.useMemo)(()=>(0,v.findHeadInCache)(M,A[1]),[M,A]),I=(0,l.useMemo)(()=>(0,w.getSelectedParams)(A),[A]),U=(0,l.useMemo)(()=>({parentTree:A,parentCacheNode:M,parentSegmentPath:null,url:p}),[A,M,p]),F=(0,l.useMemo)(()=>({tree:A,focusAndScrollRef:L,nextUrl:N}),[A,L,N]);if(null!==k){let[e,n]=k;t=(0,o.jsx)(C,{headCacheNode:e},n)}else t=null;let H=(0,o.jsxs)(y.RedirectBoundary,{children:[t,M.rsc,(0,o.jsx)(h.AppRouterAnnouncer,{tree:A})]});return H=(0,o.jsx)(f.ErrorBoundary,{errorComponent:u[0],errorStyles:u[1],children:H}),(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(j,{appRouterState:d}),(0,o.jsx)(D,{}),(0,o.jsx)(c.PathParamsContext.Provider,{value:I,children:(0,o.jsx)(c.PathnameContext.Provider,{value:O,children:(0,o.jsx)(c.SearchParamsContext.Provider,{value:R,children:(0,o.jsx)(a.GlobalLayoutRouterContext.Provider,{value:F,children:(0,o.jsx)(a.AppRouterContext.Provider,{value:_.publicAppRouterInstance,children:(0,o.jsx)(a.LayoutRouterContext.Provider,{value:U,children:H})})})})})})]})}function N(e){let{actionQueue:t,globalErrorComponentAndStyles:[n,r],assetPrefix:l}=e;return(0,R.useNavFailureHandler)(),(0,o.jsx)(f.ErrorBoundary,{errorComponent:f.default,children:(0,o.jsx)(A,{actionQueue:t,assetPrefix:l,globalError:[n,r]})})}let L=new Set,k=new Set;function D(){let[,e]=l.default.useState(0),t=L.size;return(0,l.useEffect)(()=>{let n=()=>e(e=>e+1);return k.add(n),t!==L.size&&n(),()=>{k.delete(n)}},[t,e]),[...L].map((e,t)=>(0,o.jsx)("link",{rel:"stylesheet",href:""+e,precedence:"next"},t))}globalThis._N_E_STYLE_LOAD=function(e){let t=L.size;return L.add(e),L.size!==t&&k.forEach(e=>e()),Promise.resolve()},("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},90131:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("pen",[["path",{d:"M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z",key:"1a8usu"}]])},93661:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},95796:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"HTML_LIMITED_BOT_UA_RE",{enumerable:!0,get:function(){return n}});let n=/Mediapartners-Google|Slurp|DuckDuckBot|baiduspider|yandex|sogou|bitlybot|tumblr|vkShare|quora link preview|redditbot|ia_archiver|Bingbot|BingPreview|applebot|facebookexternalhit|facebookcatalog|Twitterbot|LinkedInBot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|Yeti/i},96127:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addBasePath",{enumerable:!0,get:function(){return l}});let r=n(98834),o=n(54674);function l(e,t){return(0,o.normalizePathTrailingSlash)((0,r.addPathPrefix)(e,""))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},96474:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},96493:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"handleSegmentMismatch",{enumerable:!0,get:function(){return o}});let r=n(25232);function o(e,t,n){return(0,r.handleExternalUrl)(e,{},e.canonicalUrl,!0)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},97464:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"clearCacheNodeDataForSegmentPath",{enumerable:!0,get:function(){return function e(t,n,l){let a=l.length<=2,[i,u]=l,c=(0,o.createRouterCacheKey)(u),s=n.parallelRoutes.get(i),f=t.parallelRoutes.get(i);f&&f!==s||(f=new Map(s),t.parallelRoutes.set(i,f));let d=null==s?void 0:s.get(c),p=f.get(c);if(a){p&&p.lazyData&&p!==d||f.set(c,{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1});return}if(!p||!d){p||f.set(c,{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1});return}return p===d&&(p={lazyData:p.lazyData,rsc:p.rsc,prefetchRsc:p.prefetchRsc,head:p.head,prefetchHead:p.prefetchHead,parallelRoutes:new Map(p.parallelRoutes),loading:p.loading},f.set(c,p)),e(p,d,(0,r.getNextFlightSegmentPath)(l))}}});let r=n(74007),o=n(33123);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},97576:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{ReadonlyURLSearchParams:function(){return s},RedirectType:function(){return o.RedirectType},forbidden:function(){return a.forbidden},notFound:function(){return l.notFound},permanentRedirect:function(){return r.permanentRedirect},redirect:function(){return r.redirect},unauthorized:function(){return i.unauthorized},unstable_rethrow:function(){return u.unstable_rethrow}});let r=n(86897),o=n(49026),l=n(62765),a=n(48976),i=n(70899),u=n(163);class c extends Error{constructor(){super("Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams")}}class s extends URLSearchParams{append(){throw new c}delete(){throw new c}set(){throw new c}sort(){throw new c}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},97936:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"hmrRefreshReducer",{enumerable:!0,get:function(){return r}}),n(59008),n(57391),n(86770),n(2030),n(25232),n(59435),n(56928),n(89752),n(96493),n(68214);let r=function(e,t){return e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},98834:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addPathPrefix",{enumerable:!0,get:function(){return o}});let r=n(19169);function o(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:n,query:o,hash:l}=(0,r.parsePath)(e);return""+t+n+o+l}},99270:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])}};