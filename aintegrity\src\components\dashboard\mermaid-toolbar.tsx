'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { 
  GitBranch, 
  Workflow, 
  Calendar, 
  Users, 
  Database, 
  Network,
  ChevronDown,
  ChevronUp,
  Copy
} from 'lucide-react'

interface MermaidToolbarProps {
  onInsert: (template: string) => void
}

const mermaidTemplates = {
  flowchart: {
    name: 'Flowchart',
    icon: Workflow,
    template: `\`\`\`mermaid
flowchart TD
    A[Start] --> B{Decision?}
    B -->|Yes| C[Action 1]
    B -->|No| D[Action 2]
    C --> E[End]
    D --> E
\`\`\``
  },
  sequence: {
    name: 'Sequence Diagram',
    icon: Users,
    template: `\`\`\`mermaid
sequenceDiagram
    participant A as Alice
    participant B as Bob
    A->>B: Hello <PERSON>, how are you?
    B-->>A: Great!
    A-)B: See you later!
\`\`\``
  },
  gantt: {
    name: 'Gantt Chart',
    icon: Calendar,
    template: `\`\`\`mermaid
gantt
    title Project Timeline
    dateFormat  YYYY-MM-DD
    section Planning
    Research    :done, research, 2024-01-01, 2024-01-07
    Design      :active, design, 2024-01-08, 2024-01-15
    section Development
    Frontend    :frontend, 2024-01-16, 2024-02-15
    Backend     :backend, 2024-01-16, 2024-02-10
    Testing     :testing, after frontend, 7d
\`\`\``
  },
  gitgraph: {
    name: 'Git Graph',
    icon: GitBranch,
    template: `\`\`\`mermaid
gitgraph
    commit
    commit
    branch develop
    checkout develop
    commit
    commit
    checkout main
    merge develop
    commit
\`\`\``
  },
  erdiagram: {
    name: 'ER Diagram',
    icon: Database,
    template: `\`\`\`mermaid
erDiagram
    CUSTOMER ||--o{ ORDER : places
    ORDER ||--|{ LINE-ITEM : contains
    CUSTOMER }|..|{ DELIVERY-ADDRESS : uses
    
    CUSTOMER {
        string name
        string email
        string phone
    }
    ORDER {
        int orderNumber
        date orderDate
        string status
    }
\`\`\``
  },
  mindmap: {
    name: 'Mind Map',
    icon: Network,
    template: `\`\`\`mermaid
mindmap
  root((Project))
    Planning
      Research
      Requirements
      Timeline
    Development
      Frontend
        React
        TypeScript
      Backend
        Node.js
        Database
    Testing
      Unit Tests
      Integration
      E2E
\`\`\``
  }
}

export function MermaidToolbar({ onInsert }: MermaidToolbarProps) {
  const [isExpanded, setIsExpanded] = useState(false)

  const handleInsert = (template: string) => {
    onInsert(template)
    setIsExpanded(false)
  }

  const copyTemplate = async (template: string) => {
    try {
      await navigator.clipboard.writeText(template)
    } catch (err) {
      console.error('Failed to copy template:', err)
    }
  }

  return (
    <Card className="mb-4">
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <CardTitle className="text-sm font-medium">Mermaid Diagrams</CardTitle>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsExpanded(!isExpanded)}
            className="h-6 w-6 p-0"
          >
            {isExpanded ? (
              <ChevronUp className="h-4 w-4" />
            ) : (
              <ChevronDown className="h-4 w-4" />
            )}
          </Button>
        </div>
      </CardHeader>
      
      {isExpanded && (
        <CardContent className="pt-0">
          <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
            {Object.entries(mermaidTemplates).map(([key, template]) => {
              const Icon = template.icon
              return (
                <div key={key} className="relative group">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleInsert(template.template)}
                    className="w-full h-auto p-3 flex flex-col items-center gap-2 hover:bg-gray-700"
                  >
                    <Icon className="h-5 w-5" />
                    <span className="text-xs text-center">{template.name}</span>
                  </Button>
                  
                  {/* Copy button overlay */}
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => copyTemplate(template.template)}
                    className="absolute top-1 right-1 h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                    title="Copy template"
                  >
                    <Copy className="h-3 w-3" />
                  </Button>
                </div>
              )
            })}
          </div>
          
          <div className="mt-3 text-xs text-gray-400">
            <p>💡 <strong>Tip:</strong> Click to insert a template, or ask the AI to create diagrams for you!</p>
            <p className="mt-1">
              Try: &quot;Create a flowchart for user authentication&quot; or &quot;Show me a sequence diagram for API calls&quot;
            </p>
          </div>
        </CardContent>
      )}
    </Card>
  )
}
