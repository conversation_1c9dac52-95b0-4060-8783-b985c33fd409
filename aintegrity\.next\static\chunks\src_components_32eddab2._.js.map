{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AIntegrity/aintegrity/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AIntegrity/aintegrity/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 154, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AIntegrity/aintegrity/src/components/dashboard/chat-interface.tsx"], "sourcesContent": ["'use client'\r\n import ReactMarkdown from 'react-markdown';\r\nimport { useState, useRef, useEffect } from 'react'\r\nimport { Button } from '@/components/ui/button'\r\nimport { Input } from '@/components/ui/input'\r\nimport { Card } from '@/components/ui/card'\r\nimport { Send, Bot, User } from 'lucide-react'\r\nimport { cn } from '@/lib/utils'\r\n\r\ninterface Message {\r\n  id: string\r\n  content: string\r\n  role: 'user' | 'assistant'\r\n  timestamp: Date\r\n}\r\n\r\ninterface ChatInterfaceProps {\r\n  chatId?: string\r\n}\r\n\r\nexport function ChatInterface({ chatId: initialChatId }: ChatInterfaceProps) {\r\n  const [messages, setMessages] = useState<Message[]>([])\r\n  const [input, setInput] = useState('')\r\n  const [isLoading, setIsLoading] = useState(false)\r\n  const [chatId, setChatId] = useState<string | null>(initialChatId || null)\r\n  const [loadingChat, setLoadingChat] = useState(!!initialChatId)\r\n  const messagesEndRef = useRef<HTMLDivElement>(null)\r\n\r\n  const scrollToBottom = () => {\r\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })\r\n  }\r\n\r\n  useEffect(() => {\r\n    scrollToBottom()\r\n  }, [messages])\r\n\r\n  // Load existing chat if chatId is provided\r\n  useEffect(() => {\r\n    const loadChat = async () => {\r\n      if (!initialChatId) return\r\n\r\n      setLoadingChat(true)\r\n      try {\r\n        const response = await fetch(`/api/chats/${initialChatId}`)\r\n        if (response.ok) {\r\n          const chat = await response.json()\r\n          const chatMessages = chat.messages.map((msg: { id: string; content: string; role: string; createdAt: string }) => ({\r\n            id: msg.id,\r\n            content: msg.content,\r\n            role: msg.role as 'user' | 'assistant',\r\n            timestamp: new Date(msg.createdAt)\r\n          }))\r\n          setMessages(chatMessages)\r\n        }\r\n      } catch (error) {\r\n        console.error('Error loading chat:', error)\r\n      } finally {\r\n        setLoadingChat(false)\r\n      }\r\n    }\r\n\r\n    loadChat()\r\n  }, [initialChatId])\r\n\r\n  const sendMessage = async () => {\r\n    if (!input.trim() || isLoading) return\r\n\r\n    const userMessage: Message = {\r\n      id: Date.now().toString(),\r\n      content: input,\r\n      role: 'user',\r\n      timestamp: new Date(),\r\n    }\r\n\r\n    setMessages(prev => [...prev, userMessage])\r\n    setInput('')\r\n    setIsLoading(true)\r\n\r\n    try {\r\n      const response = await fetch('/api/chat', {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify({\r\n          message: input,\r\n          chatId,\r\n        }),\r\n      })\r\n\r\n      if (!response.ok) {\r\n        throw new Error('Failed to send message')\r\n      }\r\n\r\n      const data = await response.json()\r\n      \r\n      const assistantMessage: Message = {\r\n        id: (Date.now() + 1).toString(),\r\n        content: data.response,\r\n        role: 'assistant',\r\n        timestamp: new Date(),\r\n      }\r\n\r\n      setMessages(prev => [...prev, assistantMessage])\r\n      setChatId(data.chatId)\r\n\r\n    } catch (error) {\r\n      console.error('Error sending message:', error)\r\n      const errorMessage: Message = {\r\n        id: (Date.now() + 1).toString(),\r\n        content: 'Sorry, there was an error processing your message. Please try again.',\r\n        role: 'assistant',\r\n        timestamp: new Date(),\r\n      }\r\n      setMessages(prev => [...prev, errorMessage])\r\n    } finally {\r\n      setIsLoading(false)\r\n    }\r\n  }\r\n\r\n  const handleKeyDown = (e: React.KeyboardEvent) => {\r\n    if (e.key === 'Enter' && !e.shiftKey) {\r\n      e.preventDefault()\r\n      sendMessage()\r\n    }\r\n  }\r\n\r\n  if (loadingChat) {\r\n    return (\r\n      <div className=\"flex flex-col h-full max-w-4xl mx-auto p-4 lg:p-6\">\r\n        <div className=\"flex-1 flex items-center justify-center\">\r\n          <div className=\"text-center\">\r\n            <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4\"></div>\r\n            <p className=\"text-gray-400\">Loading chat...</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    )\r\n  }\r\n\r\n  return (\r\n    <div className=\"flex flex-col h-full max-w-4xl mx-auto p-4 lg:p-6\">\r\n      <div className=\"flex-1 overflow-auto space-y-4 mb-4\">\r\n        {messages.length === 0 ? (\r\n          <div className=\"text-center text-gray-400 mt-8\">\r\n            <Bot className=\"mx-auto h-12 w-12 mb-4\" />\r\n            <h3 className=\"text-lg font-medium text-white\">Start a conversation</h3>\r\n            <p>Ask me anything! I can help you with questions and access your saved notes.</p>\r\n          </div>\r\n        ) : (\r\n          messages.map((message) => (\r\n            <div\r\n              key={message.id}\r\n              className={cn(\r\n                'flex gap-2 lg:gap-3',\r\n                message.role === 'user' ? 'justify-end' : 'justify-start'\r\n              )}\r\n            >\r\n              {message.role === 'assistant' && (\r\n                <div className=\"flex-shrink-0\">\r\n                  <div className=\"w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center\">\r\n                    <Bot className=\"w-4 h-4 text-white\" />\r\n                  </div>\r\n                </div>\r\n              )}\r\n              \r\n              <Card className={cn(\r\n                'max-w-[85%] lg:max-w-[80%] p-3',\r\n                message.role === 'user'\r\n                  ? 'bg-blue-600 text-white'\r\n                  : 'bg-gray-800 text-gray-100'\r\n              )}>\r\n                <p className=\"whitespace-pre-wrap\"><ReactMarkdown>{message.content}</ReactMarkdown></p>\r\n              </Card>\r\n\r\n              {message.role === 'user' && (\r\n                <div className=\"flex-shrink-0\">\r\n                  <div className=\"w-8 h-8 bg-gray-700 rounded-full flex items-center justify-center\">\r\n                    <User className=\"w-4 h-4 text-gray-300\" />\r\n                  </div>\r\n                </div>\r\n              )}\r\n            </div>\r\n          ))\r\n        )}\r\n        \r\n        {isLoading && (\r\n          <div className=\"flex gap-2 lg:gap-3 justify-start\">\r\n            <div className=\"flex-shrink-0\">\r\n              <div className=\"w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center\">\r\n                <Bot className=\"w-4 h-4 text-white\" />\r\n              </div>\r\n            </div>\r\n            <Card className=\"max-w-[85%] lg:max-w-[80%] p-3 bg-gray-800 text-gray-100\">\r\n              <div className=\"flex space-x-1\">\r\n                <div className=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\"></div>\r\n                <div className=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\" style={{ animationDelay: '0.1s' }}></div>\r\n                <div className=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\" style={{ animationDelay: '0.2s' }}></div>\r\n              </div>\r\n            </Card>\r\n          </div>\r\n        )}\r\n        \r\n        <div ref={messagesEndRef} />\r\n      </div>\r\n\r\n      <div className=\"flex gap-2 items-end\">\r\n        <Input\r\n          value={input}\r\n          onChange={(e) => setInput(e.target.value)}\r\n          onKeyDown={handleKeyDown}\r\n          placeholder=\"Type your message...\"\r\n          disabled={isLoading}\r\n          className=\"flex-1 min-h-[40px]\"\r\n        />\r\n        <Button\r\n          onClick={sendMessage}\r\n          disabled={isLoading || !input.trim()}\r\n          size=\"sm\"\r\n          className=\"h-10 px-3\"\r\n        >\r\n          <Send className=\"w-4 h-4\" />\r\n        </Button>\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AACC;AACD;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;;;AAPA;;;;;;;;AAoBO,SAAS,cAAc,EAAE,QAAQ,aAAa,EAAsB;;IACzE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,iBAAiB;IACrE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,CAAC;IACjD,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAE9C,MAAM,iBAAiB;QACrB,eAAe,OAAO,EAAE,eAAe;YAAE,UAAU;QAAS;IAC9D;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR;QACF;kCAAG;QAAC;KAAS;IAEb,2CAA2C;IAC3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,MAAM;oDAAW;oBACf,IAAI,CAAC,eAAe;oBAEpB,eAAe;oBACf,IAAI;wBACF,MAAM,WAAW,MAAM,MAAM,CAAC,WAAW,EAAE,eAAe;wBAC1D,IAAI,SAAS,EAAE,EAAE;4BACf,MAAM,OAAO,MAAM,SAAS,IAAI;4BAChC,MAAM,eAAe,KAAK,QAAQ,CAAC,GAAG;iFAAC,CAAC,MAA0E,CAAC;wCACjH,IAAI,IAAI,EAAE;wCACV,SAAS,IAAI,OAAO;wCACpB,MAAM,IAAI,IAAI;wCACd,WAAW,IAAI,KAAK,IAAI,SAAS;oCACnC,CAAC;;4BACD,YAAY;wBACd;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,uBAAuB;oBACvC,SAAU;wBACR,eAAe;oBACjB;gBACF;;YAEA;QACF;kCAAG;QAAC;KAAc;IAElB,MAAM,cAAc;QAClB,IAAI,CAAC,MAAM,IAAI,MAAM,WAAW;QAEhC,MAAM,cAAuB;YAC3B,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,SAAS;YACT,MAAM;YACN,WAAW,IAAI;QACjB;QAEA,YAAY,CAAA,OAAQ;mBAAI;gBAAM;aAAY;QAC1C,SAAS;QACT,aAAa;QAEb,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,aAAa;gBACxC,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,SAAS;oBACT;gBACF;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,MAAM,mBAA4B;gBAChC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,QAAQ;gBAC7B,SAAS,KAAK,QAAQ;gBACtB,MAAM;gBACN,WAAW,IAAI;YACjB;YAEA,YAAY,CAAA,OAAQ;uBAAI;oBAAM;iBAAiB;YAC/C,UAAU,KAAK,MAAM;QAEvB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM,eAAwB;gBAC5B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,QAAQ;gBAC7B,SAAS;gBACT,MAAM;gBACN,WAAW,IAAI;YACjB;YACA,YAAY,CAAA,OAAQ;uBAAI;oBAAM;iBAAa;QAC7C,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,EAAE;YACpC,EAAE,cAAc;YAChB;QACF;IACF;IAEA,IAAI,aAAa;QACf,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;;;;;;;;;;;IAKvC;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;oBACZ,SAAS,MAAM,KAAK,kBACnB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,mMAAA,CAAA,MAAG;gCAAC,WAAU;;;;;;0CACf,6LAAC;gCAAG,WAAU;0CAAiC;;;;;;0CAC/C,6LAAC;0CAAE;;;;;;;;;;;+BAGL,SAAS,GAAG,CAAC,CAAC,wBACZ,6LAAC;4BAEC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uBACA,QAAQ,IAAI,KAAK,SAAS,gBAAgB;;gCAG3C,QAAQ,IAAI,KAAK,6BAChB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,mMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;;;;;;;;;;;8CAKrB,6LAAC,mIAAA,CAAA,OAAI;oCAAC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAChB,kCACA,QAAQ,IAAI,KAAK,SACb,2BACA;8CAEJ,cAAA,6LAAC;wCAAE,WAAU;kDAAsB,cAAA,6LAAC,2LAAA,CAAA,UAAa;sDAAE,QAAQ,OAAO;;;;;;;;;;;;;;;;gCAGnE,QAAQ,IAAI,KAAK,wBAChB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;;;;;;;2BA1BjB,QAAQ,EAAE;;;;;oBAkCpB,2BACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,mMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;;;;;;;;;;;0CAGnB,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;4CAAkD,OAAO;gDAAE,gBAAgB;4CAAO;;;;;;sDACjG,6LAAC;4CAAI,WAAU;4CAAkD,OAAO;gDAAE,gBAAgB;4CAAO;;;;;;;;;;;;;;;;;;;;;;;kCAMzG,6LAAC;wBAAI,KAAK;;;;;;;;;;;;0BAGZ,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,oIAAA,CAAA,QAAK;wBACJ,OAAO;wBACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;wBACxC,WAAW;wBACX,aAAY;wBACZ,UAAU;wBACV,WAAU;;;;;;kCAEZ,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAS;wBACT,UAAU,aAAa,CAAC,MAAM,IAAI;wBAClC,MAAK;wBACL,WAAU;kCAEV,cAAA,6LAAC,qMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAK1B;GA9MgB;KAAA", "debugId": null}}]}