{"version": 3, "sources": [], "sections": [{"offset": {"line": 87, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AIntegrity/aintegrity/src/lib/db.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 101, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AIntegrity/aintegrity/src/lib/auth.ts"], "sourcesContent": ["import { NextAuthOptions } from 'next-auth'\nimport { PrismaAdapter } from '@auth/prisma-adapter'\nimport GoogleProvider from 'next-auth/providers/google'\nimport GitHubProvider from 'next-auth/providers/github'\nimport { prisma } from './db'\n\nexport const authOptions: NextAuthOptions = {\n  adapter: PrismaAdapter(prisma),\n  providers: [\n    GoogleProvider({\n      clientId: process.env.GOOGLE_CLIENT_ID || '',\n      clientSecret: process.env.GOOGLE_CLIENT_SECRET || '',\n    }),\n    GitHubProvider({\n      clientId: process.env.GITHUB_ID || '',\n      clientSecret: process.env.GITHUB_SECRET || '',\n    }),\n  ],\n  callbacks: {\n    session: async ({ session, token }) => {\n      if (session?.user && token?.sub) {\n        session.user.id = token.sub\n      }\n      return session\n    },\n    jwt: async ({ user, token }) => {\n      if (user) {\n        token.uid = user.id\n      }\n      return token\n    },\n  },\n  session: {\n    strategy: 'jwt',\n  },\n  pages: {\n    signIn: '/auth/signin',\n  },\n}\n"], "names": [], "mappings": ";;;AACA;AACA;AACA;AACA;;;;;AAEO,MAAM,cAA+B;IAC1C,SAAS,CAAA,GAAA,oJAAA,CAAA,gBAAa,AAAD,EAAE,gHAAA,CAAA,SAAM;IAC7B,WAAW;QACT,CAAA,GAAA,mJAAA,CAAA,UAAc,AAAD,EAAE;YACb,UAAU,QAAQ,GAAG,CAAC,gBAAgB,IAAI;YAC1C,cAAc,QAAQ,GAAG,CAAC,oBAAoB,IAAI;QACpD;QACA,CAAA,GAAA,mJAAA,CAAA,UAAc,AAAD,EAAE;YACb,UAAU,QAAQ,GAAG,CAAC,SAAS,IAAI;YACnC,cAAc,QAAQ,GAAG,CAAC,aAAa,IAAI;QAC7C;KACD;IACD,WAAW;QACT,SAAS,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE;YAChC,IAAI,SAAS,QAAQ,OAAO,KAAK;gBAC/B,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,GAAG;YAC7B;YACA,OAAO;QACT;QACA,KAAK,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE;YACzB,IAAI,MAAM;gBACR,MAAM,GAAG,GAAG,KAAK,EAAE;YACrB;YACA,OAAO;QACT;IACF;IACA,SAAS;QACP,UAAU;IACZ;IACA,OAAO;QACL,QAAQ;IACV;AACF", "debugId": null}}, {"offset": {"line": 151, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AIntegrity/aintegrity/src/components/dashboard/sidebar.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Sidebar = registerClientReference(\n    function() { throw new Error(\"Attempted to call Sidebar() from the server but Sidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/dashboard/sidebar.tsx <module evaluation>\",\n    \"Sidebar\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,sEACA", "debugId": null}}, {"offset": {"line": 165, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AIntegrity/aintegrity/src/components/dashboard/sidebar.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Sidebar = registerClientReference(\n    function() { throw new Error(\"Attempted to call Sidebar() from the server but Sidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/dashboard/sidebar.tsx\",\n    \"Sidebar\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,kDACA", "debugId": null}}, {"offset": {"line": 179, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 189, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AIntegrity/aintegrity/src/components/dashboard/header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Header = registerClientReference(\n    function() { throw new Error(\"Attempted to call Header() from the server but <PERSON><PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/dashboard/header.tsx <module evaluation>\",\n    \"Header\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,SAAS,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,qEACA", "debugId": null}}, {"offset": {"line": 203, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AIntegrity/aintegrity/src/components/dashboard/header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Header = registerClientReference(\n    function() { throw new Error(\"Attempted to call Header() from the server but <PERSON><PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/dashboard/header.tsx\",\n    \"Header\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,SAAS,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,iDACA", "debugId": null}}, {"offset": {"line": 217, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 227, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AIntegrity/aintegrity/src/components/providers/mobile-nav-provider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const MobileNavProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call MobileNavProvider() from the server but MobileNavProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/providers/mobile-nav-provider.tsx <module evaluation>\",\n    \"MobileNavProvider\",\n);\nexport const useMobileNav = registerClientReference(\n    function() { throw new Error(\"Attempted to call useMobileNav() from the server but useMobileNav is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/providers/mobile-nav-provider.tsx <module evaluation>\",\n    \"useMobileNav\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,oBAAoB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,kFACA;AAEG,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,kFACA", "debugId": null}}, {"offset": {"line": 245, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AIntegrity/aintegrity/src/components/providers/mobile-nav-provider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const MobileNavProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call MobileNavProvider() from the server but MobileNavProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/providers/mobile-nav-provider.tsx\",\n    \"MobileNavProvider\",\n);\nexport const useMobileNav = registerClientReference(\n    function() { throw new Error(\"Attempted to call useMobileNav() from the server but useMobileNav is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/providers/mobile-nav-provider.tsx\",\n    \"useMobileNav\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,oBAAoB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,8DACA;AAEG,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,8DACA", "debugId": null}}, {"offset": {"line": 263, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 273, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AIntegrity/aintegrity/src/components/dashboard/mobile-sidebar.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const MobileSidebar = registerClientReference(\n    function() { throw new Error(\"Attempted to call MobileSidebar() from the server but MobileSidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/dashboard/mobile-sidebar.tsx <module evaluation>\",\n    \"MobileSidebar\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,6EACA", "debugId": null}}, {"offset": {"line": 287, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AIntegrity/aintegrity/src/components/dashboard/mobile-sidebar.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const MobileSidebar = registerClientReference(\n    function() { throw new Error(\"Attempted to call MobileSidebar() from the server but MobileSidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/dashboard/mobile-sidebar.tsx\",\n    \"MobileSidebar\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,yDACA", "debugId": null}}, {"offset": {"line": 301, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 311, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AIntegrity/aintegrity/src/app/dashboard/layout.tsx"], "sourcesContent": ["import { redirect } from 'next/navigation'\nimport { getServerSession } from 'next-auth'\nimport { authOptions } from '@/lib/auth'\nimport { Sidebar } from '@/components/dashboard/sidebar'\nimport { Header } from '@/components/dashboard/header'\nimport { MobileNavProvider } from '@/components/providers/mobile-nav-provider'\nimport { MobileSidebar } from '@/components/dashboard/mobile-sidebar'\n\nexport default async function DashboardLayout({\n  children,\n}: {\n  children: React.ReactNode\n}) {\n  const session = await getServerSession(authOptions)\n\n  if (!session) {\n    redirect('/auth/signin')\n  }\n\n  return (\n    <MobileNavProvider>\n      <div className=\"flex h-screen bg-gray-900\">\n        <Sidebar />\n        <MobileSidebar />\n        <div className=\"flex-1 flex flex-col overflow-hidden\">\n          <Header />\n          <main className=\"flex-1 overflow-auto bg-gray-900\">\n            {children}\n          </main>\n        </div>\n      </div>\n    </MobileNavProvider>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AAEe,eAAe,gBAAgB,EAC5C,QAAQ,EAGT;IACC,MAAM,UAAU,MAAM,CAAA,GAAA,qIAAA,CAAA,mBAAgB,AAAD,EAAE,kHAAA,CAAA,cAAW;IAElD,IAAI,CAAC,SAAS;QACZ,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE;IACX;IAEA,qBACE,8OAAC,4JAAA,CAAA,oBAAiB;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,0IAAA,CAAA,UAAO;;;;;8BACR,8OAAC,oJAAA,CAAA,gBAAa;;;;;8BACd,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,yIAAA,CAAA,SAAM;;;;;sCACP,8OAAC;4BAAK,WAAU;sCACb;;;;;;;;;;;;;;;;;;;;;;;AAMb", "debugId": null}}]}