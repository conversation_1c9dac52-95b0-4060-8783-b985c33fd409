"use client";

import { useEffect, useRef, useState } from "react";
import { Loader2, Maximize2 } from "lucide-react";
import { Button } from "./button";

interface MermaidProps {
  chart: string;
  className?: string;
}

export function Mermaid({ chart, className = "" }: MermaidProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const elementRef = useRef<HTMLDivElement>(null);

  // Function to sanitize Mermaid syntax
  const sanitizeMermaid = (content: string): string => {
    // Replace problematic characters in node text
    return content.replace(/\[([^\]]*)\]/g, (match, text) => {
      // Replace && with "and" to avoid syntax issues
      const sanitized = text
        .replace(/&&/g, ' and ')
        .replace(/&/g, ' and ')
        .replace(/"/g, "'")
        .replace(/\\/g, "\\\\");
      
      return `["${sanitized}"]`;
    });
  };

  useEffect(() => {
    const renderMermaid = async () => {
      if (!chart || !elementRef.current) return;
      
      setIsLoading(true);
      setError(null);
      
      try {
        // Clear previous content
        elementRef.current.innerHTML = '';
        
        // Import mermaid dynamically
        const mermaid = (await import('mermaid')).default;
        
        // Configure mermaid
        mermaid.initialize({
          startOnLoad: false,
          theme: 'dark',
          securityLevel: 'loose',
          logLevel: 'error',
          fontFamily: 'ui-sans-serif, system-ui, sans-serif',
          flowchart: {
            htmlLabels: true,
            useMaxWidth: true,
            curve: 'linear'
          }
        });
        
        // Generate unique ID
        const id = `mermaid-${Date.now()}-${Math.floor(Math.random() * 10000)}`;
        
        // Sanitize the chart content
        const sanitizedChart = sanitizeMermaid(chart);
        
        // Render the chart
        const { svg } = await mermaid.render(id, sanitizedChart);
        
        if (elementRef.current) {
          elementRef.current.innerHTML = svg;
          
          // Make SVG responsive
          const svgElement = elementRef.current.querySelector('svg');
          if (svgElement) {
            svgElement.style.width = '100%';
            svgElement.style.height = 'auto';
            svgElement.style.minHeight = '200px';
            svgElement.style.maxWidth = '100%';
          }
        }
      } catch (err) {
        console.error('Mermaid rendering error:', err);
        setError(err instanceof Error ? err.message : 'Failed to render diagram');
      } finally {
        setIsLoading(false);
      }
    };

    renderMermaid();
  }, [chart]);

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  return (
    <div className={`relative border border-gray-700 rounded-md ${className} ${isFullscreen ? 'fixed inset-0 z-50 bg-gray-900 p-4 overflow-auto' : ''}`}>
      {/* Controls */}
      <div className="absolute top-2 right-2 flex gap-2 z-10">
        <Button
          variant="ghost"
          size="icon"
          onClick={toggleFullscreen}
          className="h-8 w-8 bg-gray-800/70 hover:bg-gray-700"
        >
          <Maximize2 className="h-4 w-4" />
        </Button>
      </div>

      {/* Loading state */}
      {isLoading && (
        <div className="flex justify-center items-center h-40">
          <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
        </div>
      )}

      {/* Error state */}
      {error && (
        <div className="p-4 text-red-500 bg-red-950/20 rounded-md">
          <p className="font-mono text-sm">Error: {error}</p>
          <pre className="mt-2 p-2 bg-gray-900 rounded overflow-x-auto text-xs">
            {chart}
          </pre>
        </div>
      )}

      {/* Diagram */}
      <div 
        ref={elementRef} 
        className={`${isLoading ? 'hidden' : 'block'} p-4 overflow-x-auto min-h-[200px]`}
      />
    </div>
  );
}



